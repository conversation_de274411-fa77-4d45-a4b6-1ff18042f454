const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const vscode = require('vscode');

/**
 * 检查文件是否可写
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} 文件是否可写
 */
async function isFileWritable(filePath) {
    try {
        // 检查文件是否存在
        await fs.promises.access(filePath, fs.constants.F_OK);
        
        // 尝试打开文件进行写入测试
        const fd = await fs.promises.open(filePath, 'r+');
        await fd.close();
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 使用管理员权限执行命令
 * @param {string} command - 要执行的命令
 * @param {string[]} args - 命令参数
 * @returns {Promise<string>} 命令输出
 */
function runAsAdmin(command, args) {
    return new Promise((resolve, reject) => {
        let cmd;
        let cmdArgs;
        
        if (process.platform === 'win32') {
            // Windows使用PowerShell的Start-Process命令以管理员权限运行
            cmd = 'powershell.exe';
            cmdArgs = [
                '-Command',
                `Start-Process '${command}' -ArgumentList '${args.join(' ')}' -Verb RunAs -Wait`
            ];
        } else if (process.platform === 'darwin') {
            // macOS使用sudo
            cmd = 'sudo';
            cmdArgs = [command, ...args];
        } else {
            // Linux使用sudo
            cmd = 'sudo';
            cmdArgs = [command, ...args];
        }
        
        const process = spawn(cmd, cmdArgs, {
            stdio: 'pipe',
            shell: true
        });
        
        let stdout = '';
        let stderr = '';
        
        process.stdout.on('data', (data) => {
            stdout += data.toString();
        });
        
        process.stderr.on('data', (data) => {
            stderr += data.toString();
        });
        
        process.on('close', (code) => {
            if (code === 0) {
                resolve(stdout);
            } else {
                reject(new Error(`执行命令失败，退出代码: ${code}\n${stderr}`));
            }
        });
    });
}

/**
 * 创建提升权限的命令脚本
 * @param {string} operation - 操作类型（modify或restore）
 * @param {string} filePath - 目标文件路径
 * @returns {Promise<string>} 脚本文件路径
 */
async function createElevationScript(operation, filePath) {
    const tempDir = os.tmpdir();
    const scriptName = process.platform === 'win32' ? 'cursor_patcher.ps1' : 'cursor_patcher.sh';
    const scriptPath = path.join(tempDir, scriptName);
    
    // 获取storage.json路径
    let storageJsonPath = '';
    if (process.platform === 'win32') {
        const appData = process.env.APPDATA;
        if (appData) {
            storageJsonPath = path.join(appData, 'Cursor', 'User', 'globalStorage', 'storage.json');
        }
    } else if (process.platform === 'darwin') {
        const homeDir = os.homedir();
        storageJsonPath = path.join(homeDir, 'Library', 'Application Support', 'Cursor', 'User', 'globalStorage', 'storage.json');
    }
    
    // 获取product.json路径
    let productJsonPath = '';
    if (process.platform === 'win32') {
        const userProfile = process.env.USERPROFILE;
        if (userProfile) {
            productJsonPath = path.join(userProfile, 'AppData', 'Local', 'Programs', 'cursor', 'resources', 'app', 'product.json');
        }
    } else if (process.platform === 'darwin') {
        const homeDir = os.homedir();
        productJsonPath = path.join(homeDir, 'Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'product.json');
        
        // 如果上面的路径不存在，尝试系统级应用程序目录
        if (!fs.existsSync(productJsonPath)) {
            productJsonPath = path.join('/Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'product.json');
        }
    }
    
    // 获取workbench.desktop.main.js路径
    let workbenchJsPath = '';
    if (process.platform === 'win32') {
        const userProfile = process.env.USERPROFILE;
        if (userProfile) {
            workbenchJsPath = path.join(userProfile, 'AppData', 'Local', 'Programs', 'cursor', 'resources', 'app', 'out', 'vs', 'workbench', 'workbench.desktop.main.js');
        }
    } else if (process.platform === 'darwin') {
        const homeDir = os.homedir();
        workbenchJsPath = path.join(homeDir, 'Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'out', 'vs', 'workbench', 'workbench.desktop.main.js');
        
        // 如果上面的路径不存在，尝试系统级应用程序目录
        if (!fs.existsSync(workbenchJsPath)) {
            workbenchJsPath = path.join('/Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'out', 'vs', 'workbench', 'workbench.desktop.main.js');
        }
    }
    
    let scriptContent = '';
    
    if (process.platform === 'win32') {
        // PowerShell脚本
        scriptContent = 
'# Cursor Patcher Script\n' +
'$mainJsPath = "' + filePath.replace(/\\/g, '\\\\') + '"\n' +
'$backupPath = "' + filePath.replace(/\\/g, '\\\\') + '.backup"\n' +
'$storageJsonPath = "' + storageJsonPath.replace(/\\/g, '\\\\') + '"\n' +
'$storageBackupPath = "' + storageJsonPath.replace(/\\/g, '\\\\') + '.backup"\n' +
'$productJsonPath = "' + productJsonPath.replace(/\\/g, '\\\\') + '"\n' +
'$productBackupPath = "' + productJsonPath.replace(/\\/g, '\\\\') + '.backup"\n' +
'$workbenchJsPath = "' + workbenchJsPath.replace(/\\/g, '\\\\') + '"\n' +
'$workbenchBackupPath = "' + workbenchJsPath.replace(/\\/g, '\\\\') + '.backup"\n' +
'\n' +
'function GenerateSHA256Hash {\n' +
'    $rng = New-Object System.Security.Cryptography.RNGCryptoServiceProvider\n' +
'    $bytes = New-Object byte[](32)\n' +
'    $rng.GetBytes($bytes)\n' +
'    $sha256 = [System.Security.Cryptography.SHA256]::Create()\n' +
'    $hashBytes = $sha256.ComputeHash($bytes)\n' +
'    return [BitConverter]::ToString($hashBytes).Replace("-", "").ToLower()\n' +
'}\n' +
'\n' +
'function GenerateSHA512Hash {\n' +
'    $rng = New-Object System.Security.Cryptography.RNGCryptoServiceProvider\n' +
'    $bytes = New-Object byte[](64)\n' +
'    $rng.GetBytes($bytes)\n' +
'    $sha512 = [System.Security.Cryptography.SHA512]::Create()\n' +
'    $hashBytes = $sha512.ComputeHash($bytes)\n' +
'    return [BitConverter]::ToString($hashBytes).Replace("-", "").ToLower()\n' +
'}\n' +
'\n' +
'function GenerateUUID {\n' +
'    return [guid]::NewGuid().ToString().ToUpper()\n' +
'}\n' +
'\n' +
'try {\n' +
'    if ("' + operation + '" -eq "modify") {\n' +
'        # 修改main.js文件\n' +
'        # 创建备份\n' +
'        if (!(Test-Path $backupPath)) {\n' +
'            Copy-Item $mainJsPath $backupPath\n' +
'        }\n' +
'        \n' +
'        # 读取文件内容\n' +
'        $content = Get-Content $mainJsPath -Raw\n' +
'        \n' +
'        # 替换getMachineId函数\n' +
'        $pattern1 = "async getMachineId\\\\(\\\\)\\\\{return [^??]+\\\\?\\\\?([^}]+)\\\\}"\n' +
'        $replacement1 = "async getMachineId(){return $1}"\n' +
'        $content = $content -replace $pattern1, $replacement1\n' +
'        \n' +
'        # 替换getMacMachineId函数\n' +
'        $pattern2 = "async getMacMachineId\\\\(\\\\)\\\\{return [^??]+\\\\?\\\\?([^}]+)\\\\}"\n' +
'        $replacement2 = "async getMacMachineId(){return $1}"\n' +
'        $content = $content -replace $pattern2, $replacement2\n' +
'        \n' +
'        # 写入修改后的内容\n' +
'        $content | Set-Content $mainJsPath\n' +
'        \n' +
'        # 修改storage.json\n' +
'        $storageModified = $false\n' +
'        if (Test-Path $storageJsonPath) {\n' +
'            # 备份storage.json\n' +
'            if (!(Test-Path $storageBackupPath)) {\n' +
'                Copy-Item $storageJsonPath $storageBackupPath\n' +
'            }\n' +
'            \n' +
'            # 读取文件内容\n' +
'            $storageContent = Get-Content $storageJsonPath -Raw\n' +
'            \n' +
'            # 生成新的ID\n' +
'            $newMachineId = GenerateSHA256Hash\n' +
'            $newMacMachineId = GenerateSHA512Hash\n' +
'            $newSqmId = "{" + (GenerateUUID) + "}"\n' +
'            $newDevDeviceId = (GenerateUUID).ToLower()\n' +
'            \n' +
'            # 替换内容\n' +
'            $storageContent = $storageContent -replace \'"telemetry.machineId"\\s*:\\s*"[^"]*"\', \'"telemetry.machineId": "\' + $newMachineId + \'"\'\n' +
'            $storageContent = $storageContent -replace \'"telemetry.macMachineId"\\s*:\\s*"[^"]*"\', \'"telemetry.macMachineId": "\' + $newMacMachineId + \'"\'\n' +
'            $storageContent = $storageContent -replace \'"telemetry.sqmId"\\s*:\\s*"[^"]*"\', \'"telemetry.sqmId": "\' + $newSqmId + \'"\'\n' +
'            $storageContent = $storageContent -replace \'"telemetry.devDeviceId"\\s*:\\s*"[^"]*"\', \'"telemetry.devDeviceId": "\' + $newDevDeviceId + \'"\'\n' +
'            \n' +
'            # 写入修改后的内容\n' +
'            $storageContent | Set-Content $storageJsonPath\n' +
'            $storageModified = $true\n' +
'        }\n' +
'        \n' +
'        # 修改product.json\n' +
'        $productModified = $false\n' +
'        if (Test-Path $productJsonPath) {\n' +
'            # 备份product.json\n' +
'            if (!(Test-Path $productBackupPath)) {\n' +
'                Copy-Item $productJsonPath $productBackupPath\n' +
'            }\n' +
'            \n' +
'            # 读取文件内容\n' +
'            $productContent = Get-Content $productJsonPath -Raw\n' +
'            $productLines = $productContent.Split(\"`n\")\n' +
'            $newProductLines = @()\n' +
'            \n' +
'            # 移除包含vs/workbench/workbench.desktop.main.js的行\n' +
'            foreach ($line in $productLines) {\n' +
'                if ($line -notmatch "vs/workbench/workbench.desktop.main.js") {\n' +
'                    $newProductLines += $line\n' +
'                }\n' +
'            }\n' +
'            \n' +
'            # 重新组合内容\n' +
'            $newProductContent = $newProductLines -join \"`n\"\n' +
'            \n' +
'            # 写入修改后的内容\n' +
'            $newProductContent | Set-Content $productJsonPath\n' +
'            $productModified = $true\n' +
'        }\n' +
'        \n' +
'        # 修改workbench.desktop.main.js\n' +
'        $workbenchModified = $false\n' +
'        if (Test-Path $workbenchJsPath) {\n' +
'            # 备份workbench.desktop.main.js\n' +
'            if (!(Test-Path $workbenchBackupPath)) {\n' +
'                Copy-Item $workbenchJsPath $workbenchBackupPath\n' +
'            }\n' +
'            \n' +
'            # 移除workbench.desktop.main.js\n' +
'            if (Test-Path $workbenchJsPath) {\n' +
'                Remove-Item $workbenchJsPath -Force\n' +
'            }\n' +
'            $workbenchModified = $true\n' +
'        }\n' +
'        \n' +
'        $message = "请重启Cursor应用。"\n' +
'        if ($storageModified) { $message += " storage.json也已修改。" }\n' +
'        if ($productModified) { $message += " product.json也已修改。" }\n' +
'        if ($workbenchModified) { $message += " workbench.desktop.main.js也已修改。" }\n' +
'        if (!$storageModified -or !$productModified -or !$workbenchModified) { $message += " 警告: 某些文件修改失败，插件功能可能不完整。" }\n' +
'        $message += "请重启Cursor应用。"\n' +
'        \n' +
'        Write-Output $message\n' +
'    } else {\n' +
'        # 恢复main.js\n' +
'        # 检查备份文件是否存在\n' +
'        if (Test-Path $backupPath) {\n' +
'            # 恢复备份\n' +
'            Copy-Item $backupPath $mainJsPath -Force\n' +
'            \n' +
'            # 恢复storage.json\n' +
'            $storageRestored = $false\n' +
'            if ((Test-Path $storageJsonPath) -and (Test-Path $storageBackupPath)) {\n' +
'                Copy-Item $storageBackupPath $storageJsonPath -Force\n' +
'                $storageRestored = $true\n' +
'            }\n' +
'            \n' +
'            # 恢复product.json\n' +
'            $productRestored = $false\n' +
'            if ((Test-Path $productJsonPath) -and (Test-Path $productBackupPath)) {\n' +
'                Copy-Item $productBackupPath $productJsonPath -Force\n' +
'                $productRestored = $true\n' +
'            }\n' +
'            \n' +
'            # 恢复workbench.desktop.main.js\n' +
'            $workbenchRestored = $false\n' +
'            if ((Test-Path $workbenchJsPath) -and (Test-Path $workbenchBackupPath)) {\n' +
'                Copy-Item $workbenchBackupPath $workbenchJsPath -Force\n' +
'                $workbenchRestored = $true\n' +
'            }\n' +
'            \n' +
'            $message = "请重启Cursor应用。"\n' +
'            if ($storageRestored) { $message += " storage.json也已恢复。" }\n' +
'            if ($productRestored) { $message += " product.json也已恢复。" }\n' +
'            if ($workbenchRestored) { $message += " workbench.desktop.main.js也已恢复。" }\n' +
'            if (!$storageRestored -or !$productRestored -or !$workbenchRestored) { $message += " 警告: 某些文件恢复失败。" }\n' +
'            $message += "请重启Cursor应用。"\n' +
'            \n' +
'            Write-Output $message\n' +
'        } else {\n' +
'            Write-Error "未找到备份文件，无法恢复"\n' +
'            exit 1\n' +
'        }\n' +
'    }\n' +
'} catch {\n' +
'    Write-Error $_\n' +
'    exit 1\n' +
'}';
    } else {
        // Bash脚本
        scriptContent = `#!/bin/bash
set -e

MAIN_JS_PATH="${filePath}"
BACKUP_PATH="${filePath}.backup"
STORAGE_JSON_PATH="${storageJsonPath}"
STORAGE_BACKUP_PATH="${storageJsonPath}.backup"
PRODUCT_JSON_PATH="${productJsonPath}"
PRODUCT_BACKUP_PATH="${productJsonPath}.backup"
WORKBENCH_JS_PATH="${workbenchJsPath}"
WORKBENCH_BACKUP_PATH="${workbenchJsPath}.backup"

# 生成SHA-256哈希
generate_sha256_hash() {
    openssl rand -hex 32
}

# 生成SHA-512哈希
generate_sha512_hash() {
    openssl rand -hex 64
}

# 生成UUID
generate_uuid() {
    uuidgen
}

if [ "${operation}" = "modify" ]; then
    # 修改main.js文件
    # 创建备份
    if [ ! -f "$BACKUP_PATH" ]; then
        cp "$MAIN_JS_PATH" "$BACKUP_PATH"
    fi
    
    # 读取文件内容
    content=$(cat "$MAIN_JS_PATH")
    
    # 替换getMachineId函数
    content=$(echo "$content" | perl -pe 's/async getMachineId\\(\\)\\{return [^??]+\\?\\?([^}]+)\\}/async getMachineId(){return $1}/g')
    
    # 替换getMacMachineId函数
    content=$(echo "$content" | perl -pe 's/async getMacMachineId\\(\\)\\{return [^??]+\\?\\?([^}]+)\\}/async getMacMachineId(){return $1}/g')
    
    # 写入修改后的内容
    echo "$content" > "$MAIN_JS_PATH"
    
    # 修改storage.json
    storage_modified=false
    if [ -f "$STORAGE_JSON_PATH" ]; then
        # 备份storage.json
        if [ ! -f "$STORAGE_BACKUP_PATH" ]; then
            cp "$STORAGE_JSON_PATH" "$STORAGE_BACKUP_PATH"
        fi
        
        # 读取文件内容
        storage_content=$(cat "$STORAGE_JSON_PATH")
        
        # 生成新的ID
        new_machine_id=$(generate_sha256_hash)
        new_mac_machine_id=$(generate_sha512_hash)
        new_sqm_id="{$(generate_uuid | tr a-z A-Z)}"
        new_dev_device_id=$(generate_uuid | tr A-Z a-z)
        
        # 替换内容
        storage_content=$(echo "$storage_content" | perl -pe 's/"telemetry\\.machineId"\\s*:\\s*"[^"]*"/"telemetry.machineId": "'$new_machine_id'"/g')
        storage_content=$(echo "$storage_content" | perl -pe 's/"telemetry\\.macMachineId"\\s*:\\s*"[^"]*"/"telemetry.macMachineId": "'$new_mac_machine_id'"/g')
        storage_content=$(echo "$storage_content" | perl -pe 's/"telemetry\\.sqmId"\\s*:\\s*"[^"]*"/"telemetry.sqmId": "'$new_sqm_id'"/g')
        storage_content=$(echo "$storage_content" | perl -pe 's/"telemetry\\.devDeviceId"\\s*:\\s*"[^"]*"/"telemetry.devDeviceId": "'$new_dev_device_id'"/g')
        
        # 写入修改后的内容
        echo "$storage_content" > "$STORAGE_JSON_PATH"
        storage_modified=true
    fi
    
    # 修改product.json
    product_modified=false
    if [ -f "$PRODUCT_JSON_PATH" ]; then
        # 备份product.json
        if [ ! -f "$PRODUCT_BACKUP_PATH" ]; then
            cp "$PRODUCT_JSON_PATH" "$PRODUCT_BACKUP_PATH"
        fi
        
        # 移除包含vs/workbench/workbench.desktop.main.js的行
        cat "$PRODUCT_JSON_PATH" | grep -v "vs/workbench/workbench.desktop.main.js" > "$PRODUCT_JSON_PATH.tmp"
        mv "$PRODUCT_JSON_PATH.tmp" "$PRODUCT_JSON_PATH"
        product_modified=true
    fi
    
    # 修改workbench.desktop.main.js
    workbench_modified=false
    if [ -f "$WORKBENCH_JS_PATH" ]; then
        # 备份workbench.desktop.main.js
        if [ ! -f "$WORKBENCH_BACKUP_PATH" ]; then
            cp "$WORKBENCH_JS_PATH" "$WORKBENCH_BACKUP_PATH"
        fi
        
        # 移除workbench.desktop.main.js
        if [ -f "$WORKBENCH_JS_PATH" ]; then
            rm "$WORKBENCH_JS_PATH"
        fi
        workbench_modified=true
    fi
    
    message="请重启Cursor应用。"
    if [ "$storage_modified" = true ]; then message="$message storage.json也已修改。"; fi
    if [ "$product_modified" = true ]; then message="$message product.json也已修改。"; fi
    if [ "$workbench_modified" = true ]; then message="$message workbench.desktop.main.js也已修改。"; fi
    if [ "$storage_modified" = false ] || [ "$product_modified" = false ] || [ "$workbench_modified" = false ]; then message="$message 警告: 某些文件修改失败，插件功能可能不完整。"; fi
    message="$message 请重启Cursor应用。"
else
    # 恢复main.js
    # 检查备份文件是否存在
    if [ -f "$BACKUP_PATH" ]; then
        # 恢复备份
        cp "$BACKUP_PATH" "$MAIN_JS_PATH"
        
        # 恢复storage.json
        storage_restored=false
        if [ -f "$STORAGE_JSON_PATH" ] && [ -f "$STORAGE_BACKUP_PATH" ]; then
            cp "$STORAGE_BACKUP_PATH" "$STORAGE_JSON_PATH"
            storage_restored=true
        fi
        
        # 恢复product.json
        product_restored=false
        if [ -f "$PRODUCT_JSON_PATH" ] && [ -f "$PRODUCT_BACKUP_PATH" ]; then
            cp "$PRODUCT_BACKUP_PATH" "$PRODUCT_JSON_PATH"
            product_restored=true
        fi
        
        # 恢复workbench.desktop.main.js
        workbench_restored=false
        if [ -f "$WORKBENCH_JS_PATH" ] && [ -f "$WORKBENCH_BACKUP_PATH" ]; then
            cp "$WORKBENCH_BACKUP_PATH" "$WORKBENCH_JS_PATH"
            workbench_restored=true
        fi
        
        message="请重启Cursor应用。"
        if [ "$storage_restored" = true ]; then message="$message storage.json也已恢复。"; fi
        if [ "$product_restored" = true ]; then message="$message product.json也已恢复。"; fi
        if [ "$workbench_restored" = true ]; then message="$message workbench.desktop.main.js也已恢复。"; fi
        if [ "$storage_restored" = false ] || [ "$product_restored" = false ] || [ "$workbench_restored" = false ]; then message="$message 警告: 某些文件恢复失败。"; fi
        message="$message 请重启Cursor应用。"
    else
        echo "未找到备份文件，无法恢复" >&2
        exit 1
    fi
fi

echo "$message"
`;
    }
    
    await fs.promises.writeFile(scriptPath, scriptContent);
    
    // 在Linux/Mac上设置可执行权限
    if (process.platform !== 'win32') {
        await fs.promises.chmod(scriptPath, '755');
    }
    
    return scriptPath;
}

/**
 * 提升权限修改Cursor的main.js文件
 * @param {string} operation - 操作类型（modify或restore）
 * @param {string} filePath - 目标文件路径
 * @returns {Promise<string>} 操作结果
 */
async function elevateAndModify(operation, filePath) {
    try {
        // 先检查文件是否可写
        const writable = await isFileWritable(filePath);
        
        if (writable) {
            // 如果文件可写，则不需要提升权限
            return null;
        }
        
        // 创建提升权限的脚本
        const scriptPath = await createElevationScript(operation, filePath);
        
        // 执行脚本
        let result;
        if (process.platform === 'win32') {
            result = await runAsAdmin('powershell.exe', ['-ExecutionPolicy', 'Bypass', '-File', scriptPath]);
        } else {
            result = await runAsAdmin('bash', [scriptPath]);
        }
        
        return result.trim();
    } catch (error) {
        throw new Error(`提升权限操作失败: ${error.message}`);
    }
}

module.exports = {
    elevateAndModify,
    isFileWritable
}; 