const vscode = require('vscode');
const { getNonce } = require('../utils/common');

/**
 * 生成AI聊天界面的HTML内容
 * @param {vscode.Webview} webview - Webview实例
 * @param {vscode.Uri} extensionUri - 扩展URI
 * @returns {string} HTML内容
 */
function getAiChatWebviewContent(webview, extensionUri) {
    const nonce = getNonce();
    const iconPath = webview.asWebviewUri(vscode.Uri.joinPath(extensionUri, 'icon.png'));
    
    return `<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src vscode-resource: https: data:; script-src 'nonce-${nonce}'; style-src 'unsafe-inline';">
        <title>Cursor-Sun AI 聊天</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                padding: 0;
                margin: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
            }
            
            .chat-header {
                display: flex;
                align-items: center;
                padding: 10px 16px;
                border-bottom: 1px solid var(--vscode-panel-border);
                background-color: var(--vscode-editor-background);
            }
            
            .chat-header img {
                width: 24px;
                height: 24px;
                margin-right: 10px;
            }
            
            .chat-header h1 {
                margin: 0;
                font-size: 16px;
                font-weight: 500;
            }
            
            .model-selector {
                margin-left: auto;
                display: flex;
                align-items: center;
            }
            
            .model-selector select {
                background-color: var(--vscode-dropdown-background);
                color: var(--vscode-dropdown-foreground);
                border: 1px solid var(--vscode-dropdown-border);
                padding: 4px 8px;
                border-radius: 2px;
                font-size: 12px;
                outline: none;
            }
            
            .chat-container {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
                display: flex;
                flex-direction: column;
                gap: 16px;
            }
            
            .message {
                display: flex;
                flex-direction: column;
                max-width: 90%;
                animation: fadeIn 0.3s ease;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            .user-message {
                align-self: flex-end;
            }
            
            .ai-message {
                align-self: flex-start;
            }
            
            .message-header {
                display: flex;
                align-items: center;
                margin-bottom: 4px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }
            
            .user-message .message-header {
                justify-content: flex-end;
            }
            
            .message-bubble {
                padding: 10px 14px;
                border-radius: 12px;
                font-size: 14px;
                line-height: 1.5;
                white-space: pre-wrap;
                word-break: break-word;
            }
            
            .user-message .message-bubble {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border-top-right-radius: 4px;
            }
            
            .ai-message .message-bubble {
                background-color: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                border-top-left-radius: 4px;
            }
            
            .input-container {
                display: flex;
                padding: 16px;
                border-top: 1px solid var(--vscode-panel-border);
                background-color: var(--vscode-editor-background);
            }
            
            .input-box {
                flex: 1;
                display: flex;
                background-color: var(--vscode-input-background);
                border: 1px solid var(--vscode-input-border);
                border-radius: 6px;
                padding: 4px;
            }
            
            .input-box textarea {
                flex: 1;
                border: none;
                outline: none;
                background-color: transparent;
                color: var(--vscode-input-foreground);
                font-family: inherit;
                font-size: 14px;
                resize: none;
                padding: 8px 12px;
                min-height: 40px;
                max-height: 200px;
            }
            
            .send-button {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                border-radius: 4px;
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                margin-left: 8px;
                transition: all 0.2s ease;
            }
            
            .send-button:hover {
                background-color: var(--vscode-button-hoverBackground);
            }
            
            .send-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
            
            .send-button svg {
                width: 18px;
                height: 18px;
                fill: currentColor;
            }
            
            .typing-indicator {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 6px 12px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
                animation: fadeIn 0.3s ease;
            }
            
            .typing-dot {
                width: 6px;
                height: 6px;
                background-color: var(--vscode-descriptionForeground);
                border-radius: 50%;
                animation: typingAnimation 1.4s infinite ease-in-out;
            }
            
            .typing-dot:nth-child(1) { animation-delay: 0s; }
            .typing-dot:nth-child(2) { animation-delay: 0.2s; }
            .typing-dot:nth-child(3) { animation-delay: 0.4s; }
            
            @keyframes typingAnimation {
                0%, 100% { transform: translateY(0); }
                50% { transform: translateY(-4px); }
            }
            
            .code-block {
                position: relative;
                background-color: var(--vscode-textCodeBlock-background);
                border-radius: 6px;
                margin: 10px 0;
                overflow: hidden;
            }
            
            .code-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 6px 12px;
                background-color: rgba(0,0,0,0.2);
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }
            
            .code-content {
                padding: 12px;
                overflow-x: auto;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 13px;
                line-height: 1.5;
                tab-size: 2;
            }
            
            .copy-button {
                background: transparent;
                border: none;
                color: var(--vscode-descriptionForeground);
                cursor: pointer;
                font-size: 12px;
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 2px 6px;
                border-radius: 4px;
                transition: all 0.2s ease;
            }
            
            .copy-button:hover {
                background-color: rgba(255,255,255,0.1);
            }
            
            .copy-button svg {
                width: 12px;
                height: 12px;
                fill: currentColor;
            }
            
            .welcome-message {
                text-align: center;
                padding: 40px 20px;
                color: var(--vscode-descriptionForeground);
                font-size: 14px;
            }
            
            .welcome-message h2 {
                margin-bottom: 16px;
                font-weight: 500;
                font-size: 18px;
                color: var(--vscode-foreground);
            }
            
            .welcome-message p {
                margin-bottom: 12px;
                line-height: 1.6;
            }
            
            .welcome-message .models {
                display: flex;
                justify-content: center;
                gap: 16px;
                margin-top: 20px;
            }
            
            .welcome-message .model-card {
                background-color: var(--vscode-input-background);
                border: 1px solid var(--vscode-input-border);
                border-radius: 6px;
                padding: 12px 16px;
                width: 160px;
            }
            
            .welcome-message .model-name {
                font-weight: 500;
                margin-bottom: 4px;
                color: var(--vscode-foreground);
            }
            
            .welcome-message .model-desc {
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }
        </style>
    </head>
    <body>
        <div class="chat-header">
            <img src="${iconPath}" alt="Cursor-Sun Logo">
            <h1>Cursor-Sun AI 聊天</h1>
            <div class="model-selector">
                <select id="modelSelector">
                    <option value="deepseek-v3.1" selected>deepseek-v3.1</option>
                    <option value="gpt-4o-mini">gpt-4o-mini</option>
                </select>
            </div>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="welcome-message">
                <h2>欢迎使用 Cursor-Sun AI 聊天</h2>
                <p>您可以在这里与AI助手进行对话，获取编程帮助或解答问题。</p>
                <p>支持的模型:</p>
                <div class="models">
                    <div class="model-card">
                        <div class="model-name">deepseek-v3.1</div>
                        <div class="model-desc">默认模型，强大的中英双语能力</div>
                    </div>
                    <div class="model-card">
                        <div class="model-name">gpt-4o-mini</div>
                        <div class="model-desc">OpenAI的高效模型</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-box">
                <textarea id="messageInput" placeholder="输入消息，按Enter发送..." rows="1"></textarea>
            </div>
            <button id="sendButton" class="send-button" disabled>
                <svg viewBox="0 0 24 24">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path>
                </svg>
            </button>
        </div>
        
        <script nonce="${nonce}">
            (function() {
                const vscode = acquireVsCodeApi();
                const chatContainer = document.getElementById('chatContainer');
                const messageInput = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');
                const modelSelector = document.getElementById('modelSelector');
                
                let messageCounter = 0;
                let isProcessing = false;
                
                // 调整输入框高度
                function adjustTextareaHeight() {
                    messageInput.style.height = 'auto';
                    messageInput.style.height = (messageInput.scrollHeight) + 'px';
                }
                
                // 检查输入是否为空
                function checkInput() {
                    sendButton.disabled = messageInput.value.trim() === '' || isProcessing;
                }
                
                // 滚动到底部
                function scrollToBottom() {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
                
                // 添加用户消息
                function addUserMessage(text) {
                    const messageId = 'msg-' + messageCounter++;
                    
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message user-message';
                    messageDiv.id = messageId;
                    
                    messageDiv.innerHTML = \`
                        <div class="message-header">您</div>
                        <div class="message-bubble">\${escapeHtml(text)}</div>
                    \`;
                    
                    chatContainer.appendChild(messageDiv);
                    scrollToBottom();
                    
                    return messageId;
                }
                
                // 添加AI消息
                function addAiMessage() {
                    const messageId = 'msg-' + messageCounter++;
                    
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message ai-message';
                    messageDiv.id = messageId;
                    
                    messageDiv.innerHTML = \`
                        <div class="message-header">AI 助手</div>
                        <div class="message-bubble"></div>
                    \`;
                    
                    chatContainer.appendChild(messageDiv);
                    
                    // 添加打字指示器
                    const typingIndicator = document.createElement('div');
                    typingIndicator.className = 'typing-indicator';
                    typingIndicator.id = messageId + '-typing';
                    typingIndicator.innerHTML = \`
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    \`;
                    
                    chatContainer.appendChild(typingIndicator);
                    scrollToBottom();
                    
                    return messageId;
                }
                
                // 更新AI消息内容
                function updateAiMessage(messageId, text) {
                    const messageBubble = document.querySelector('#' + messageId + ' .message-bubble');
                    if (messageBubble) {
                        // 处理markdown格式
                        let formattedText = text;
                        
                        // 处理代码块
                        formattedText = processCodeBlocks(formattedText);
                        
                        // 处理粗体
                        formattedText = formattedText.replace(/\\*\\*([^\\*]+)\\*\\*/g, '<strong>$1</strong>');
                        
                        // 处理斜体
                        formattedText = formattedText.replace(/\\*([^\\*]+)\\*/g, '<em>$1</em>');
                        
                        // 处理行内代码
                        formattedText = formattedText.replace(/\`([^\`]+)\`/g, '<code style="background-color: rgba(0,0,0,0.1); padding: 2px 4px; border-radius: 3px;">$1</code>');
                        
                        // 处理链接
                        formattedText = formattedText.replace(/\\[([^\\]]+)\\]\\(([^\\)]+)\\)/g, '<a href="$2" style="color: var(--vscode-textLink-foreground);">$1</a>');
                        
                        // 处理换行
                        formattedText = formattedText.replace(/\\n/g, '<br>');
                        
                        messageBubble.innerHTML = formattedText;
                    }
                }
                
                // 处理代码块
                function processCodeBlocks(text) {
                    const codeBlockRegex = /\`\`\`([a-zA-Z]*)(\\n[\\s\\S]*?)\`\`\`/g;
                    return text.replace(codeBlockRegex, function(match, language, code) {
                        code = code.substring(1); // 移除第一个换行符
                        return \`
                            <div class="code-block">
                                <div class="code-header">
                                    <span>\${language || 'code'}</span>
                                    <button class="copy-button" onclick="copyCode(this)">
                                        <svg viewBox="0 0 24 24">
                                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                        </svg>
                                        复制
                                    </button>
                                </div>
                                <pre class="code-content">\${escapeHtml(code)}</pre>
                            </div>
                        \`;
                    });
                }
                
                // 移除打字指示器
                function removeTypingIndicator(messageId) {
                    const typingIndicator = document.getElementById(messageId + '-typing');
                    if (typingIndicator) {
                        typingIndicator.remove();
                    }
                }
                
                // 转义HTML
                function escapeHtml(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                }
                
                // 发送消息
                function sendMessage() {
                    const text = messageInput.value.trim();
                    if (text === '' || isProcessing) return;
                    
                    // 清空欢迎消息
                    if (document.querySelector('.welcome-message')) {
                        document.querySelector('.welcome-message').remove();
                    }
                    
                    isProcessing = true;
                    checkInput();
                    
                    const userMessageId = addUserMessage(text);
                    const aiMessageId = addAiMessage();
                    
                    const selectedModel = modelSelector.value;
                    
                    vscode.postMessage({
                        command: 'sendMessage',
                        userMessage: text,
                        model: selectedModel,
                        messageId: aiMessageId
                    });
                    
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    checkInput();
                }
                
                // 复制代码
                window.copyCode = function(button) {
                    const codeBlock = button.closest('.code-block');
                    const codeContent = codeBlock.querySelector('.code-content').textContent;
                    
                    navigator.clipboard.writeText(codeContent).then(() => {
                        const originalText = button.innerHTML;
                        button.innerHTML = '<svg viewBox="0 0 24 24"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg> 已复制';
                        
                        setTimeout(() => {
                            button.innerHTML = originalText;
                        }, 2000);
                    });
                };
                
                // 事件监听
                messageInput.addEventListener('input', () => {
                    adjustTextareaHeight();
                    checkInput();
                });
                
                messageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (!sendButton.disabled) {
                            sendMessage();
                        }
                    }
                });
                
                sendButton.addEventListener('click', sendMessage);
                
                // 接收来自扩展的消息
                window.addEventListener('message', event => {
                    const message = event.data;
                    
                    switch (message.command) {
                        case 'streamStart':
                            // 流开始，已经创建了消息框
                            break;
                            
                        case 'streamChunk':
                            const currentText = document.querySelector('#' + message.messageId + ' .message-bubble').textContent;
                            updateAiMessage(message.messageId, currentText + message.content);
                            scrollToBottom();
                            break;
                            
                        case 'streamEnd':
                            removeTypingIndicator(message.messageId);
                            isProcessing = false;
                            checkInput();
                            break;
                            
                        case 'error':
                            removeTypingIndicator(message.messageId);
                            updateAiMessage(message.messageId, '❌ 错误: ' + message.error);
                            isProcessing = false;
                            checkInput();
                            break;
                    }
                });
                
                // 初始化
                messageInput.focus();
                checkInput();
            }());
        </script>
    </body>
    </html>`;
}

module.exports = { getAiChatWebviewContent }; 