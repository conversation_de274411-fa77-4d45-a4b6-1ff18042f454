# Cursor-Sun 扩展

Cursor-Sun 是一个功能强大的 Cursor 编辑器增强工具，提供多种实用功能以提升您的 Cursor 使用体验。

## 主要功能

1. **智能令牌管理**
   - 自动检查令牌使用情况，及时更新令牌
   - 无缝同步令牌到 Cursor 数据库
   - 避免令牌耗尽导致的使用中断

2. **Cursor 增强**
   - 绕过机器 ID 检查
   - 自定义设备标识
   - 提升为企业级许可证

3. **AI 聊天辅助**
   - 提供便捷的 AI 聊天界面
   - 支持自定义聊天指令
   - 实时响应您的需求

## 使用方法

### 安装

1. 在 VSCode/Cursor 扩展商店搜索 "Cursor-Sun" 并安装
2. 或下载 .vsix 文件，通过 "从 VSIX 安装..." 选项安装

### 快速入门

1. 安装后，点击活动栏中的 Cursor-Sun 图标打开侧边栏
2. 使用提供的账号密码登录
3. 点击 "启用插件" 按钮激活增强功能
4. 重启 Cursor 应用以使更改生效

### 常见操作

- **启用/停用插件**：点击相应按钮启用或停用插件功能
- **更新令牌**：点击 "更新令牌" 按钮手动获取新令牌
- **查看令牌使用情况**：登录后自动显示在用户信息区域

## 注意事项

- 启用或停用插件后，需要重启 Cursor 应用才能生效
- 首次登录后需要手动获取一次令牌，后续会自动更新
- 如遇问题，可尝试重新启用插件或更新令牌

## 技术支持

如有任何问题或建议，请通过以下方式联系我们：

- GitHub 仓库：[https://github.com/cursor-sun/cursor-sun-vscode](https://github.com/cursor-sun/cursor-sun-vscode)
- 电子邮件：<EMAIL>

## 许可证

MIT