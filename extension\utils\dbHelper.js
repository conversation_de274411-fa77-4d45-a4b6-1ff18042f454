const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const os = require('os');

class DBHelper {
    constructor() {
        this.dbPath = path.join(os.homedir(), '.cursor-sun.db');
        this.db = null;
    }

    connect() {
        try {
            this.db = new Database(this.dbPath);
            this.initTables();
            return true;
        } catch (error) {
            console.error('数据库连接失败:', error);
            return false;
        }
    }

    initTables() {
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                password TEXT NOT NULL,
                token TEXT,
                expiry INTEGER,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                updated_at INTEGER DEFAULT (strftime('%s', 'now'))
            )
        `;

        try {
            this.db.exec(createTableSQL);
            return true;
        } catch (error) {
            console.error('创建表失败:', error);
            return false;
        }
    }

    saveUser(username, password, token = null, expiry = null) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO users (username, password, token, expiry, updated_at)
            VALUES (?, ?, ?, ?, strftime('%s', 'now'))
        `);

        try {
            stmt.run(username, password, token, expiry);
            return true;
        } catch (error) {
            console.error('保存用户信息失败:', error);
            return false;
        }
    }

    getUser() {
        const stmt = this.db.prepare('SELECT * FROM users ORDER BY updated_at DESC LIMIT 1');
        try {
            return stmt.get();
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }

    clearUser() {
        const stmt = this.db.prepare('DELETE FROM users');
        try {
            stmt.run();
            return true;
        } catch (error) {
            console.error('清除用户信息失败:', error);
            return false;
        }
    }

    close() {
        if (this.db) {
            try {
                this.db.close();
                return true;
            } catch (error) {
                console.error('关闭数据库连接失败:', error);
                return false;
            }
        }
        return true;
    }
}

module.exports = DBHelper; 