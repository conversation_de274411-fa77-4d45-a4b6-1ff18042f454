{"inputs": {"utils/common.js": {"bytes": 403, "imports": [], "format": "cjs"}, "webview/webviewContent.js": {"bytes": 34647, "imports": [{"path": "utils/common.js", "kind": "require-call", "original": "../utils/common"}], "format": "cjs"}, "utils/workbenchModifier.js": {"bytes": 19574, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}], "format": "cjs"}, "node_modules/webidl-conversions/lib/index.js": {"bytes": 5056, "imports": [], "format": "cjs"}, "node_modules/whatwg-url/lib/utils.js": {"bytes": 562, "imports": [], "format": "cjs"}, "node_modules/tr46/lib/mappingTable.json": {"bytes": 260049, "imports": []}, "node_modules/tr46/index.js": {"bytes": 7567, "imports": [{"path": "punycode", "kind": "require-call", "external": true}, {"path": "node_modules/tr46/lib/mappingTable.json", "kind": "require-call", "original": "./lib/mappingTable.json"}], "format": "cjs"}, "node_modules/whatwg-url/lib/url-state-machine.js": {"bytes": 33573, "imports": [{"path": "punycode", "kind": "require-call", "external": true}, {"path": "node_modules/tr46/index.js", "kind": "require-call", "original": "tr46"}], "format": "cjs"}, "node_modules/whatwg-url/lib/URL-impl.js": {"bytes": 3804, "imports": [{"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}], "format": "cjs"}, "node_modules/whatwg-url/lib/URL.js": {"bytes": 4212, "imports": [{"path": "node_modules/webidl-conversions/lib/index.js", "kind": "require-call", "original": "webidl-conversions"}, {"path": "node_modules/whatwg-url/lib/utils.js", "kind": "require-call", "original": "./utils.js"}, {"path": "node_modules/whatwg-url/lib/URL-impl.js", "kind": "require-call", "original": ".//URL-impl.js"}], "format": "cjs"}, "node_modules/whatwg-url/lib/public-api.js": {"bytes": 625, "imports": [{"path": "node_modules/whatwg-url/lib/URL.js", "kind": "require-call", "original": "./URL"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}, {"path": "node_modules/whatwg-url/lib/url-state-machine.js", "kind": "require-call", "original": "./url-state-machine"}], "format": "cjs"}, "node_modules/safer-buffer/safer.js": {"bytes": 2110, "imports": [{"path": "buffer", "kind": "require-call", "external": true}], "format": "cjs"}, "node_modules/iconv-lite/lib/bom-handling.js": {"bytes": 1109, "imports": [], "format": "cjs"}, "node_modules/iconv-lite/encodings/internal.js": {"bytes": 6309, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}, {"path": "string_decoder", "kind": "require-call", "external": true}], "format": "cjs"}, "node_modules/iconv-lite/encodings/utf32.js": {"bytes": 9982, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}], "format": "cjs"}, "node_modules/iconv-lite/encodings/utf16.js": {"bytes": 5502, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}], "format": "cjs"}, "node_modules/iconv-lite/encodings/utf7.js": {"bytes": 9283, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}], "format": "cjs"}, "node_modules/iconv-lite/encodings/sbcs-codec.js": {"bytes": 2191, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}], "format": "cjs"}, "node_modules/iconv-lite/encodings/sbcs-data.js": {"bytes": 5116, "imports": [], "format": "cjs"}, "node_modules/iconv-lite/encodings/sbcs-data-generated.js": {"bytes": 32034, "imports": [], "format": "cjs"}, "node_modules/iconv-lite/encodings/dbcs-codec.js": {"bytes": 23065, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}], "format": "cjs"}, "node_modules/iconv-lite/encodings/tables/shiftjis.json": {"bytes": 23782, "imports": []}, "node_modules/iconv-lite/encodings/tables/eucjp.json": {"bytes": 41064, "imports": []}, "node_modules/iconv-lite/encodings/tables/cp936.json": {"bytes": 47320, "imports": []}, "node_modules/iconv-lite/encodings/tables/gbk-added.json": {"bytes": 1247, "imports": []}, "node_modules/iconv-lite/encodings/tables/gb18030-ranges.json": {"bytes": 2216, "imports": []}, "node_modules/iconv-lite/encodings/tables/cp949.json": {"bytes": 38122, "imports": []}, "node_modules/iconv-lite/encodings/tables/cp950.json": {"bytes": 42356, "imports": []}, "node_modules/iconv-lite/encodings/tables/big5-added.json": {"bytes": 17717, "imports": []}, "node_modules/iconv-lite/encodings/dbcs-data.js": {"bytes": 9389, "imports": [{"path": "node_modules/iconv-lite/encodings/tables/shiftjis.json", "kind": "require-call", "original": "./tables/shiftjis.json"}, {"path": "node_modules/iconv-lite/encodings/tables/eucjp.json", "kind": "require-call", "original": "./tables/eucjp.json"}, {"path": "node_modules/iconv-lite/encodings/tables/cp936.json", "kind": "require-call", "original": "./tables/cp936.json"}, {"path": "node_modules/iconv-lite/encodings/tables/cp936.json", "kind": "require-call", "original": "./tables/cp936.json"}, {"path": "node_modules/iconv-lite/encodings/tables/gbk-added.json", "kind": "require-call", "original": "./tables/gbk-added.json"}, {"path": "node_modules/iconv-lite/encodings/tables/cp936.json", "kind": "require-call", "original": "./tables/cp936.json"}, {"path": "node_modules/iconv-lite/encodings/tables/gbk-added.json", "kind": "require-call", "original": "./tables/gbk-added.json"}, {"path": "node_modules/iconv-lite/encodings/tables/gb18030-ranges.json", "kind": "require-call", "original": "./tables/gb18030-ranges.json"}, {"path": "node_modules/iconv-lite/encodings/tables/cp949.json", "kind": "require-call", "original": "./tables/cp949.json"}, {"path": "node_modules/iconv-lite/encodings/tables/cp950.json", "kind": "require-call", "original": "./tables/cp950.json"}, {"path": "node_modules/iconv-lite/encodings/tables/cp950.json", "kind": "require-call", "original": "./tables/cp950.json"}, {"path": "node_modules/iconv-lite/encodings/tables/big5-added.json", "kind": "require-call", "original": "./tables/big5-added.json"}], "format": "cjs"}, "node_modules/iconv-lite/encodings/index.js": {"bytes": 733, "imports": [{"path": "node_modules/iconv-lite/encodings/internal.js", "kind": "require-call", "original": "./internal"}, {"path": "node_modules/iconv-lite/encodings/utf32.js", "kind": "require-call", "original": "./utf32"}, {"path": "node_modules/iconv-lite/encodings/utf16.js", "kind": "require-call", "original": "./utf16"}, {"path": "node_modules/iconv-lite/encodings/utf7.js", "kind": "require-call", "original": "./utf7"}, {"path": "node_modules/iconv-lite/encodings/sbcs-codec.js", "kind": "require-call", "original": "./sbcs-codec"}, {"path": "node_modules/iconv-lite/encodings/sbcs-data.js", "kind": "require-call", "original": "./sbcs-data"}, {"path": "node_modules/iconv-lite/encodings/sbcs-data-generated.js", "kind": "require-call", "original": "./sbcs-data-generated"}, {"path": "node_modules/iconv-lite/encodings/dbcs-codec.js", "kind": "require-call", "original": "./dbcs-codec"}, {"path": "node_modules/iconv-lite/encodings/dbcs-data.js", "kind": "require-call", "original": "./dbcs-data"}], "format": "cjs"}, "node_modules/iconv-lite/lib/streams.js": {"bytes": 3380, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}], "format": "cjs"}, "node_modules/iconv-lite/lib/index.js": {"bytes": 6321, "imports": [{"path": "node_modules/safer-buffer/safer.js", "kind": "require-call", "original": "safer-buffer"}, {"path": "node_modules/iconv-lite/lib/bom-handling.js", "kind": "require-call", "original": "./bom-handling"}, {"path": "node_modules/iconv-lite/encodings/index.js", "kind": "require-call", "original": "../encodings"}, {"path": "node_modules/iconv-lite/lib/streams.js", "kind": "require-call", "original": "./streams"}, {"path": "stream", "kind": "require-call", "external": true}], "format": "cjs"}, "node_modules/encoding/lib/encoding.js": {"bytes": 2116, "imports": [{"path": "node_modules/iconv-lite/lib/index.js", "kind": "require-call", "original": "iconv-lite"}], "format": "cjs"}, "node_modules/node-fetch/lib/index.js": {"bytes": 45775, "imports": [{"path": "stream", "kind": "require-call", "external": true}, {"path": "http", "kind": "require-call", "external": true}, {"path": "url", "kind": "require-call", "external": true}, {"path": "node_modules/whatwg-url/lib/public-api.js", "kind": "require-call", "original": "whatwg-url"}, {"path": "https", "kind": "require-call", "external": true}, {"path": "zlib", "kind": "require-call", "external": true}, {"path": "node_modules/encoding/lib/encoding.js", "kind": "require-call", "original": "encoding"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/util.js": {"bytes": 331, "imports": [], "format": "cjs"}, "node_modules/better-sqlite3/lib/sqlite-error.js": {"bytes": 717, "imports": [], "format": "cjs"}, "node_modules/file-uri-to-path/index.js": {"bytes": 1723, "imports": [{"path": "path", "kind": "require-call", "external": true}], "format": "cjs"}, "node_modules/bindings/bindings.js": {"bytes": 5986, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "node_modules/file-uri-to-path/index.js", "kind": "require-call", "original": "file-uri-to-path"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/wrappers.js": {"bytes": 1145, "imports": [{"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/transaction.js": {"bytes": 2663, "imports": [{"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/pragma.js": {"bytes": 536, "imports": [{"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/backup.js": {"bytes": 2380, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "util", "kind": "require-call", "external": true}, {"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/serialize.js": {"bytes": 625, "imports": [{"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/function.js": {"bytes": 1396, "imports": [{"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/aggregate.js": {"bytes": 1932, "imports": [{"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/table.js": {"bytes": 7144, "imports": [{"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "../util"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/methods/inspect.js": {"bytes": 174, "imports": [], "format": "cjs"}, "node_modules/better-sqlite3/lib/database.js": {"bytes": 4116, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "node_modules/better-sqlite3/lib/util.js", "kind": "require-call", "original": "./util"}, {"path": "node_modules/better-sqlite3/lib/sqlite-error.js", "kind": "require-call", "original": "./sqlite-error"}, {"path": "node_modules/bindings/bindings.js", "kind": "require-call", "original": "bindings"}, {"path": "node_modules/better-sqlite3/lib/methods/wrappers.js", "kind": "require-call", "original": "./methods/wrappers"}, {"path": "node_modules/better-sqlite3/lib/methods/transaction.js", "kind": "require-call", "original": "./methods/transaction"}, {"path": "node_modules/better-sqlite3/lib/methods/pragma.js", "kind": "require-call", "original": "./methods/pragma"}, {"path": "node_modules/better-sqlite3/lib/methods/backup.js", "kind": "require-call", "original": "./methods/backup"}, {"path": "node_modules/better-sqlite3/lib/methods/serialize.js", "kind": "require-call", "original": "./methods/serialize"}, {"path": "node_modules/better-sqlite3/lib/methods/function.js", "kind": "require-call", "original": "./methods/function"}, {"path": "node_modules/better-sqlite3/lib/methods/aggregate.js", "kind": "require-call", "original": "./methods/aggregate"}, {"path": "node_modules/better-sqlite3/lib/methods/table.js", "kind": "require-call", "original": "./methods/table"}, {"path": "node_modules/better-sqlite3/lib/methods/inspect.js", "kind": "require-call", "original": "./methods/inspect"}], "format": "cjs"}, "node_modules/better-sqlite3/lib/index.js": {"bytes": 110, "imports": [{"path": "node_modules/better-sqlite3/lib/database.js", "kind": "require-call", "original": "./database"}, {"path": "node_modules/better-sqlite3/lib/sqlite-error.js", "kind": "require-call", "original": "./sqlite-error"}], "format": "cjs"}, "utils/dbHelper.js": {"bytes": 2556, "imports": [{"path": "node_modules/better-sqlite3/lib/index.js", "kind": "require-call", "original": "better-sqlite3"}, {"path": "path", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}], "format": "cjs"}, "utils/tokenChecker.js": {"bytes": 6084, "imports": [{"path": "https", "kind": "require-call", "external": true}, {"path": "node_modules/better-sqlite3/lib/index.js", "kind": "require-call", "original": "better-sqlite3"}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}], "format": "cjs"}, "utils/settingsManager.js": {"bytes": 4319, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}], "format": "cjs"}, "utils/elevatePermissions.js": {"bytes": 21483, "imports": [{"path": "child_process", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}], "format": "cjs"}, "utils/storageModifier.js": {"bytes": 7507, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "crypto", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}], "format": "cjs"}, "utils/productModifier.js": {"bytes": 6962, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}], "format": "cjs"}, "utils/cursorPatcher.js": {"bytes": 7654, "imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "utils/elevatePermissions.js", "kind": "require-call", "original": "./elevatePermissions"}, {"path": "utils/storageModifier.js", "kind": "require-call", "original": "./storageModifier"}, {"path": "utils/productModifier.js", "kind": "require-call", "original": "./productModifier"}, {"path": "utils/workbenchModifier.js", "kind": "require-call", "original": "./workbenchModifier"}], "format": "cjs"}, "handlers/messageHandler.js": {"bytes": 17162, "imports": [{"path": "vscode", "kind": "require-call", "external": true}, {"path": "node_modules/node-fetch/lib/index.js", "kind": "require-call", "original": "node-fetch"}, {"path": "utils/dbHelper.js", "kind": "require-call", "original": "../utils/dbHelper"}, {"path": "utils/tokenChecker.js", "kind": "require-call", "original": "../utils/tokenChecker"}, {"path": "utils/settingsManager.js", "kind": "require-call", "original": "../utils/settingsManager"}, {"path": "utils/workbenchModifier.js", "kind": "require-call", "original": "../utils/workbenchModifier"}, {"path": "utils/cursorPatcher.js", "kind": "require-call", "original": "../utils/cursorPatcher"}], "format": "cjs"}, "providers/sidebarProvider.js": {"bytes": 4770, "imports": [{"path": "vscode", "kind": "require-call", "external": true}, {"path": "webview/webviewContent.js", "kind": "require-call", "original": "../webview/webviewContent"}, {"path": "utils/workbenchModifier.js", "kind": "require-call", "original": "../utils/workbenchModifier"}, {"path": "handlers/messageHandler.js", "kind": "require-call", "original": "../handlers/messageHandler"}, {"path": "<runtime>", "kind": "import-statement", "external": true}], "format": "cjs"}, "handlers/aiChatHandler.js": {"bytes": 3845, "imports": [], "format": "cjs"}, "webview/aiChatContent.js": {"bytes": 23309, "imports": [{"path": "vscode", "kind": "require-call", "external": true}, {"path": "utils/common.js", "kind": "require-call", "original": "../utils/common"}], "format": "cjs"}, "extension.js": {"bytes": 22267, "imports": [{"path": "vscode", "kind": "require-call", "external": true}, {"path": "providers/sidebarProvider.js", "kind": "require-call", "original": "./providers/sidebarProvider"}, {"path": "handlers/messageHandler.js", "kind": "require-call", "original": "./handlers/messageHandler"}, {"path": "handlers/aiChatHandler.js", "kind": "require-call", "original": "./handlers/aiChatHandler"}, {"path": "webview/webviewContent.js", "kind": "require-call", "original": "./webview/webviewContent"}, {"path": "webview/aiChatContent.js", "kind": "require-call", "original": "./webview/aiChatContent"}, {"path": "utils/tokenChecker.js", "kind": "require-call", "original": "./utils/tokenChecker"}, {"path": "utils/workbenchModifier.js", "kind": "require-call", "original": "./utils/workbenchModifier"}, {"path": "utils/settingsManager.js", "kind": "require-call", "original": "./utils/settingsManager"}, {"path": "utils/workbenchModifier.js", "kind": "require-call", "original": "./utils/workbenchModifier"}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "handlers/messageHandler.js", "kind": "require-call", "original": "./handlers/messageHandler"}], "format": "cjs"}}, "outputs": {"dist/extension.js": {"imports": [{"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "punycode", "kind": "require-call", "external": true}, {"path": "punycode", "kind": "require-call", "external": true}, {"path": "buffer", "kind": "require-call", "external": true}, {"path": "string_decoder", "kind": "require-call", "external": true}, {"path": "stream", "kind": "require-call", "external": true}, {"path": "stream", "kind": "require-call", "external": true}, {"path": "http", "kind": "require-call", "external": true}, {"path": "url", "kind": "require-call", "external": true}, {"path": "https", "kind": "require-call", "external": true}, {"path": "zlib", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "util", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "https", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "crypto", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "child_process", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}, {"path": "path", "kind": "require-call", "external": true}, {"path": "os", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "vscode", "kind": "require-call", "external": true}, {"path": "fs", "kind": "require-call", "external": true}], "exports": [], "entryPoint": "extension.js", "inputs": {"utils/common.js": {"bytesInOutput": 214}, "webview/webviewContent.js": {"bytesInOutput": 36833}, "utils/workbenchModifier.js": {"bytesInOutput": 12103}, "node_modules/webidl-conversions/lib/index.js": {"bytesInOutput": 2418}, "node_modules/whatwg-url/lib/utils.js": {"bytesInOutput": 424}, "node_modules/tr46/lib/mappingTable.json": {"bytesInOutput": 260054}, "node_modules/tr46/index.js": {"bytesInOutput": 4888}, "node_modules/whatwg-url/lib/url-state-machine.js": {"bytesInOutput": 16687}, "node_modules/whatwg-url/lib/URL-impl.js": {"bytesInOutput": 2317}, "node_modules/whatwg-url/lib/URL.js": {"bytesInOutput": 2754}, "node_modules/whatwg-url/lib/public-api.js": {"bytesInOutput": 329}, "node_modules/safer-buffer/safer.js": {"bytesInOutput": 1238}, "node_modules/iconv-lite/lib/bom-handling.js": {"bytesInOutput": 587}, "node_modules/iconv-lite/encodings/internal.js": {"bytesInOutput": 2277}, "node_modules/iconv-lite/encodings/utf32.js": {"bytesInOutput": 3594}, "node_modules/iconv-lite/encodings/utf16.js": {"bytesInOutput": 2263}, "node_modules/iconv-lite/encodings/utf7.js": {"bytesInOutput": 3472}, "node_modules/iconv-lite/encodings/sbcs-codec.js": {"bytesInOutput": 1050}, "node_modules/iconv-lite/encodings/sbcs-data.js": {"bytesInOutput": 5443}, "node_modules/iconv-lite/encodings/sbcs-data-generated.js": {"bytesInOutput": 57252}, "node_modules/iconv-lite/encodings/dbcs-codec.js": {"bytesInOutput": 7280}, "node_modules/iconv-lite/encodings/tables/shiftjis.json": {"bytesInOutput": 45733}, "node_modules/iconv-lite/encodings/tables/eucjp.json": {"bytesInOutput": 79573}, "node_modules/iconv-lite/encodings/tables/cp936.json": {"bytesInOutput": 86959}, "node_modules/iconv-lite/encodings/tables/gbk-added.json": {"bytesInOutput": 1639}, "node_modules/iconv-lite/encodings/tables/gb18030-ranges.json": {"bytesInOutput": 2244}, "node_modules/iconv-lite/encodings/tables/cp949.json": {"bytesInOutput": 69177}, "node_modules/iconv-lite/encodings/tables/cp950.json": {"bytesInOutput": 82234}, "node_modules/iconv-lite/encodings/tables/big5-added.json": {"bytesInOutput": 35519}, "node_modules/iconv-lite/encodings/dbcs-data.js": {"bytesInOutput": 1791}, "node_modules/iconv-lite/encodings/index.js": {"bytesInOutput": 213}, "node_modules/iconv-lite/lib/streams.js": {"bytesInOutput": 1414}, "node_modules/iconv-lite/lib/index.js": {"bytesInOutput": 2219}, "node_modules/encoding/lib/encoding.js": {"bytesInOutput": 730}, "node_modules/node-fetch/lib/index.js": {"bytesInOutput": 19067}, "node_modules/better-sqlite3/lib/util.js": {"bytesInOutput": 249}, "node_modules/better-sqlite3/lib/sqlite-error.js": {"bytesInOutput": 508}, "node_modules/file-uri-to-path/index.js": {"bytesInOutput": 450}, "node_modules/bindings/bindings.js": {"bytesInOutput": 2547}, "node_modules/better-sqlite3/lib/methods/wrappers.js": {"bytesInOutput": 756}, "node_modules/better-sqlite3/lib/methods/transaction.js": {"bytesInOutput": 1437}, "node_modules/better-sqlite3/lib/methods/pragma.js": {"bytesInOutput": 389}, "node_modules/better-sqlite3/lib/methods/backup.js": {"bytesInOutput": 1474}, "node_modules/better-sqlite3/lib/methods/serialize.js": {"bytesInOutput": 422}, "node_modules/better-sqlite3/lib/methods/function.js": {"bytesInOutput": 869}, "node_modules/better-sqlite3/lib/methods/aggregate.js": {"bytesInOutput": 1113}, "node_modules/better-sqlite3/lib/methods/table.js": {"bytesInOutput": 4159}, "node_modules/better-sqlite3/lib/methods/inspect.js": {"bytesInOutput": 114}, "node_modules/better-sqlite3/lib/database.js": {"bytesInOutput": 2294}, "node_modules/better-sqlite3/lib/index.js": {"bytesInOutput": 78}, "utils/dbHelper.js": {"bytesInOutput": 1705}, "utils/tokenChecker.js": {"bytesInOutput": 2997}, "utils/settingsManager.js": {"bytesInOutput": 1749}, "utils/elevatePermissions.js": {"bytesInOutput": 16943}, "utils/storageModifier.js": {"bytesInOutput": 3304}, "utils/productModifier.js": {"bytesInOutput": 3020}, "utils/cursorPatcher.js": {"bytesInOutput": 2822}, "handlers/messageHandler.js": {"bytesInOutput": 7280}, "providers/sidebarProvider.js": {"bytesInOutput": 1888}, "handlers/aiChatHandler.js": {"bytesInOutput": 1250}, "webview/aiChatContent.js": {"bytesInOutput": 23628}, "extension.js": {"bytesInOutput": 11014}}, "bytes": 946684}}}