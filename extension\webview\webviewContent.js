const { getNonce } = require('../utils/common');

/**
 * 生成主Webview的HTML内容
 * @param {vscode.Uri} iconSrc - 图标资源URI
 * @param {Object} currentUser - 当前用户信息
 * @param {boolean} isPluginActive - 插件是否激活
 * @returns {string} HTML内容
 */
function getWebviewContent(iconSrc, currentUser, isPluginActive = false) {
    const nonce = getNonce();
    const initialUserJson = JSON.stringify(currentUser || null);
    const initialPluginActiveJson = JSON.stringify(isPluginActive);

    // 规范化用户信息显示
    let userInfoHtml = '未登录';
    if (currentUser && currentUser.username) {
        userInfoHtml = `账号: ${currentUser.username}<br>`;
        
        // 显示API返回的到期时间（如果存在）
        if (currentUser.apiResponse && currentUser.apiResponse.data && currentUser.apiResponse.data.expireTime) {
            const expireTime = new Date(currentUser.apiResponse.data.expireTime);
            userInfoHtml += `到期时间: ${expireTime.toLocaleString()}<br>`;
        }
        
        userInfoHtml += `登录时间: ${new Date(currentUser.loginTime).toLocaleString()}<br>`;
        
        if (currentUser.apiResponse) {
            userInfoHtml += `登录状态: ${currentUser.apiResponse.message || '成功'}<br>`;
        }
    }

    return `<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src vscode-resource: https: data:; script-src 'nonce-${nonce}'; style-src vscode-resource: 'unsafe-inline' https:;">
        <title>Cursor-Sun</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                padding: 10px;
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
                margin: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }
            .container {
                display: flex;
                flex-direction: column;
                gap: 15px;
                width: 100%;
                flex-grow: 1;
            }
            .header {
                display: flex;
                align-items: center;
                gap: 8px;
                padding-bottom: 10px;
                border-bottom: 1px solid var(--vscode-panel-border);
            }
            .header img { width: 24px; height: 24px; }
            .header h1 { margin: 0; font-size: 16px; }

            .user-info-container {
                background-color: var(--vscode-input-background);
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
                border: 1px solid var(--vscode-input-border, var(--vscode-panel-border));
            }
            .user-info-container h3 { margin-top: 0; margin-bottom: 5px; font-size: 14px; }
            #userInfo { min-height: 20px; }

            .notice-box {
                background-color: var(--vscode-editorInfo-background, rgba(0, 122, 204, 0.1));
                color: var(--vscode-editorInfo-foreground, var(--vscode-foreground));
                padding: 10px 12px;
                border-radius: 4px;
                font-size: 12px;
                border-left: 3px solid var(--vscode-infoForeground, #3794ff);
                margin-bottom: 5px;
                line-height: 1.4;
            }

            .buttons-container { display: flex; flex-direction: column; gap: 8px; }
            button {
                padding: 8px 10px; font-size: 12px; cursor: pointer;
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: 1px solid var(--vscode-button-border, transparent);
                border-radius: 2px; text-align: left; display: flex;
                align-items: center; gap: 6px;
                transition: all 0.2s ease;
            }
            button:hover { 
                background-color: var(--vscode-button-hoverBackground); 
                transform: translateY(-1px);
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            }
            button svg { width: 14px; height: 14px; fill: currentColor; }
            
            .announcement {
                margin-top: 15px; padding: 10px; font-size: 12px;
                background-color: var(--vscode-input-background);
                border-radius: 4px;
                border: 1px solid var(--vscode-input-border, var(--vscode-panel-border));
            }
            .announcement h3 { margin-top: 0; margin-bottom: 5px; font-size: 14px; }

            /* 美化后的登录面板样式 */
            #loginPanel {
                display: none; /* 初始隐藏 */
                flex-direction: column;
                gap: 20px;
                padding: 25px;
                background-color: var(--vscode-editor-background);
                border-radius: 8px;
                border: 1px solid var(--vscode-panel-border);
                box-shadow: 0 8px 24px rgba(0,0,0,0.2);
                max-width: 350px;
                margin: auto;
                position: relative;
                animation: fadeIn 0.3s ease;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            #loginPanel::before {
                content: '';
                position: absolute;
                top: -40px;
                left: 50%;
                transform: translateX(-50%);
                width: 70px;
                height: 70px;
                background-color: var(--vscode-button-background);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1;
            }
            
            #loginPanel::after {
                content: '👤';
                position: absolute;
                top: -25px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 28px;
                z-index: 2;
            }
            
            #loginPanel h2 {
                text-align: center;
                color: var(--vscode-editor-foreground);
                margin-top: 20px;
                margin-bottom: 15px;
                font-size: 20px;
                font-weight: 500;
            }
            
            .input-group { 
                margin-bottom: 5px;
                position: relative;
            }
            
            .input-group label {
                display: block;
                margin-bottom: 8px;
                font-size: 13px;
                color: var(--vscode-foreground);
                font-weight: 500;
            }
            
            .input-group input {
                width: 100%;
                padding: 12px 15px;
                border-radius: 4px;
                border: 1px solid var(--vscode-input-border, #ccc);
                background-color: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                font-size: 14px;
                transition: all 0.2s ease;
                box-sizing: border-box;
            }
            
            .input-group input:focus {
                outline: none;
                border-color: var(--vscode-focusBorder);
                box-shadow: 0 0 0 2px rgba(0,122,204,0.2);
            }
            
            #loginErrorMessage { 
                color: var(--vscode-errorForeground); 
                font-size: 13px; 
                text-align: center; 
                min-height: 20px; 
                margin-top: 10px;
                margin-bottom: 5px;
                font-weight: 500;
            }
            
            .login-buttons { 
                display: flex; 
                gap: 12px; 
                justify-content: space-between;
                margin-top: 10px;
            }
            
            #confirmLoginBtn {
                flex-grow: 1;
                padding: 10px;
                font-weight: 500;
                font-size: 14px;
                text-align: center;
                justify-content: center;
                background-color: var(--vscode-button-background);
                transition: all 0.2s ease;
            }
            
            #confirmLoginBtn:hover {
                background-color: var(--vscode-button-hoverBackground);
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            
            #cancelLoginBtn {
                padding: 10px 15px;
                font-weight: 500;
                font-size: 14px;
                background-color: var(--vscode-button-secondaryBackground);
                color: var(--vscode-button-secondaryForeground);
                border: 1px solid var(--vscode-button-border, transparent);
                transition: all 0.2s ease;
            }
            
            #cancelLoginBtn:hover {
                background-color: var(--vscode-button-secondaryHoverBackground);
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }

            /* 隐藏主面板当登录显示时 */
            .login-active #mainPanel { display: none; }
            .login-active #loginPanel { display: flex; }

            /* 添加停用插件按钮样式 */
            .plugin-active {
                background-color: var(--vscode-errorForeground) !important;
                border-color: var(--vscode-errorForeground) !important;
            }
        </style>
    </head>
    <body class="${currentUser ? '' : 'login-active'}">
        <div id="mainPanel" class="container">
            <div class="header">
                <img src="${iconSrc}" alt="Cursor-Sun Logo">
                <h1>Cursor-Sun</h1>
            </div>
            
            <div class="user-info-container">
                <h3>用户信息</h3>
                <div id="userInfo">${userInfoHtml}</div>
            </div>
            
            <div class="buttons-container">
                <button id="authBtn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>
                    <span id="authBtnText">${currentUser ? '退出登录' : '登录'}</span>
                </button>
                
                <div class="notice-box">
                    启用和停用插件后请重启一次cursor，才能生效。
                </div>
                
                <button id="startPluginBtn" class="${isPluginActive ? 'plugin-active' : ''}"><svg viewBox="0 0 24 24"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>${isPluginActive ? '停用插件' : '启用插件'}</button>
                
                <div class="notice-box">
                    只有第一次登录需要手动获取令牌，平时都会自动获取，除非遇到封号、封IP等情况才需再次手动获取。
                </div>
                
                <button id="updateTokenBtn"><svg viewBox="0 0 24 24"><path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"></path></svg>更新令牌</button>
                <button id="enableApiModeBtn"><svg viewBox="0 0 24 24"><path d="M22 12h-4l-3 9L9 3l-3 9H2"></path></svg>启用API模式</button>
                <button id="aiChatBtn">
                    <svg viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-14h2v7h-2zm0 8h2v2h-2z"/>
                    </svg>
                    辅助AI聊天
                </button>
                <button id="enhanceBtn"><svg viewBox="0 0 24 24"><path d="M9 21c0 .5.4 1 1 1h4c.6 0 1-.5 1-1v-1H9v1zm3-19C8.1 2 5 5.1 5 9c0 2.4 1.2 4.5 3 5.7V17c0 .5.4 1 1 1h6c.6 0 1-.5 1-1v-2.3c1.8-1.3 3-3.4 3-5.7 0-3.9-3.1-7-7-7z"/></svg>开启提示词增强</button>
            </div>
            
            <div class="announcement">
                <h3>公告</h3>
                <p>欢迎使用 Cursor-Sun 插件！</p>
                <p>2.19.2更新</p>
                <p>1、修复claude-sonnet-4模型不存在的问题</p>
                <p>2、修复claude-opus-4模型不存在的问题</p>
                <p>3、修复4.1问题</p>
                <p>4、暂停api功能</p>   
                <p>5、修复辅助聊天功能</p>               
            </div>
        </div>

        <div id="loginPanel">
            <h2>插件登录</h2>
            <div class="input-group">
                <label for="username">账号</label>
                <input type="text" id="username" name="username" placeholder="请输入您的账号" required>
            </div>
            <div class="input-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请输入您的密码" required>
            </div>
            <div id="loginErrorMessage"></div>
            <div class="login-buttons">
                <button id="confirmLoginBtn">确认登录</button>
                <button id="cancelLoginBtn">返回</button>
            </div>
        </div>

        <script nonce="${nonce}">
            (function() {
                const vscode = acquireVsCodeApi();
                let currentUser = JSON.parse('${initialUserJson}');
                let isPluginActive = JSON.parse('${initialPluginActiveJson}');

                const body = document.body;
                const mainPanel = document.getElementById('mainPanel');
                const loginPanel = document.getElementById('loginPanel');
                
                const authBtn = document.getElementById('authBtn');
                const authBtnText = document.getElementById('authBtnText');
                const userInfoDiv = document.getElementById('userInfo');

                const usernameInput = document.getElementById('username');
                const passwordInput = document.getElementById('password');
                const confirmLoginBtn = document.getElementById('confirmLoginBtn');
                const cancelLoginBtn = document.getElementById('cancelLoginBtn');
                const loginErrorMessageDiv = document.getElementById('loginErrorMessage');

                function updateUIForLoginState() {
                    if (currentUser && currentUser.username) {
                        authBtnText.textContent = '退出登录';
                        
                        let displayInfo = \`账号: \${currentUser.username}\`;
                        
                        // 显示API返回的到期时间（如果存在）
                        if (currentUser.apiResponse && currentUser.apiResponse.data && currentUser.apiResponse.data.expireTime) {
                            const expireTime = new Date(currentUser.apiResponse.data.expireTime);
                            displayInfo += \`<br>到期时间: \${expireTime.toLocaleString()}\`;
                        }
                        
                        displayInfo += \`<br>登录时间: \${new Date(currentUser.loginTime).toLocaleString()}\`;
                        
                        if (currentUser.apiResponse) {
                            displayInfo += \`<br>登录状态: \${currentUser.apiResponse.message || '成功'}\`;
                        }
                        
                        // 只在插件启用状态下显示令牌使用情况
                        if (isPluginActive && currentUser.tokenUsageInfo) {
                            displayInfo += \`<br><br><span style="color: var(--vscode-statusBarItem-warningBackground, #FFCC00);">⚡ 令牌使用情况</span>\`;
                            displayInfo += \`<br><div style="display: flex; justify-content: space-between;"><span>请求次数: \${currentUser.tokenUsageInfo.requestCount || 0}</span><span>令牌限制: \${currentUser.tokenUsageInfo.maxRequests+350 || '无限制'}</span></div>\`;
                            
                            // 计算已使用百分比
                            const usedPercent = Math.round((currentUser.tokenUsageInfo.requestCount / (currentUser.tokenUsageInfo.maxRequests+350)) * 100);
                            if (!isNaN(usedPercent)) {
                                const barColor = usedPercent > 80 ? '#FF5252' : (usedPercent > 50 ? '#FFCC00' : '#4CAF50');
                                displayInfo += \`
                                <div style="margin-top: 4px; margin-bottom: 2px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2px;">
                                        <span style="font-size: 10px;">使用比例</span>
                                        <span style="font-size: 10px; font-weight: bold;">\${usedPercent}%</span>
                                    </div>
                                    <div style="background-color: var(--vscode-input-background); height: 6px; width: 100%; border-radius: 3px;">
                                        <div style="background-color: \${barColor}; height: 100%; width: \${usedPercent}%; border-radius: 3px;"></div>
                                    </div>
                                </div>\`;
                            }
                        }
                        
                        userInfoDiv.innerHTML = displayInfo;
                        body.classList.remove('login-active');
                    } else {
                        authBtnText.textContent = '登录';
                        userInfoDiv.textContent = '未登录';
                    }
                    loginErrorMessageDiv.textContent = ''; // 清除之前的错误
                }
                
                // 初始UI设置
                updateUIForLoginState();
                if (!currentUser) { // 如果未登录，默认显示登录面板
                     body.classList.add('login-active');
                }

                // 输入框按下回车键时触发登录
                passwordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        confirmLoginBtn.click();
                    }
                });

                authBtn.addEventListener('click', () => {
                    if (currentUser && currentUser.username) { // 如果已登录，则退出
                        vscode.postMessage({ command: 'doLogout' });
                    } else { // 如果未登录，显示登录面板
                        body.classList.add('login-active');
                        usernameInput.focus();
                    }
                });

                confirmLoginBtn.addEventListener('click', () => {
                    const username = usernameInput.value;
                    const password = passwordInput.value;
                    if (!username || !password) {
                        loginErrorMessageDiv.textContent = '请输入账号和密码';
                        return;
                    }
                    loginErrorMessageDiv.textContent = '登录中...';
                    vscode.postMessage({
                        command: 'submitLogin',
                        username: username,
                        password: password
                    });
                });

                cancelLoginBtn.addEventListener('click', () => {
                    body.classList.remove('login-active');
                    loginErrorMessageDiv.textContent = '';
                });
                
                // 标准按钮
                const startPluginBtn = document.getElementById('startPluginBtn');
                
                // 根据初始状态设置按钮
                if (isPluginActive) {
                    startPluginBtn.classList.add('plugin-active');
                }

                startPluginBtn.addEventListener('click', () => {
                    isPluginActive = !isPluginActive;
                    // 保留SVG图标，只更新文本部分
                    const buttonSvg = startPluginBtn.querySelector('svg');
                    startPluginBtn.innerHTML = '';
                    startPluginBtn.appendChild(buttonSvg);
                    
                    // 禁用按钮，显示加载状态
                    startPluginBtn.disabled = true;
                    startPluginBtn.appendChild(document.createTextNode(isPluginActive ? '正在启用插件...' : '正在停用插件...'));
                    
                    // 发送命令到扩展，请求修改Cursor main.js文件
                    // 当enable为true时，修改文件；当enable为false时，从备份恢复文件
                    vscode.postMessage({ 
                        command: 'togglePlugin', 
                        enable: isPluginActive
                    });
                });

                document.getElementById('updateTokenBtn').addEventListener('click', () => {
                    if (!currentUser || !currentUser.username) {
                        vscode.postMessage({ command: 'info', text: '请先登录' });
                        return;
                    }
                    
                    // 检查插件是否已启用
                    if (!isPluginActive || startPluginBtn.textContent.trim() === '启用插件') {
                        vscode.postMessage({ command: 'info', text: '请先启用插件才能更换令牌' });
                        return;
                    }
                    
                    // 禁用按钮，显示加载状态
                    const updateTokenBtn = document.getElementById('updateTokenBtn');
                    const originalText = updateTokenBtn.innerHTML;
                    const buttonSvg = updateTokenBtn.querySelector('svg');
                    updateTokenBtn.innerHTML = '';
                    updateTokenBtn.appendChild(buttonSvg);
                    updateTokenBtn.appendChild(document.createTextNode('获取中...'));
                    updateTokenBtn.disabled = true;
                    
                    // 发送获取令牌的命令
                    vscode.postMessage({ command: 'updateToken' });
                    
                    // 如果3秒后没有响应，恢复按钮状态
                    setTimeout(() => {
                        if (updateTokenBtn.textContent.includes('获取中')) {
                            updateTokenBtn.innerHTML = originalText;
                            updateTokenBtn.disabled = false;
                        }
                    }, 3000);
                });
                
                const enableApiModeBtn = document.getElementById('enableApiModeBtn');
                let isApiModeActive = false;

                enableApiModeBtn.addEventListener('click', () => {
                    isApiModeActive = !isApiModeActive;
                    // 保留SVG图标，只更新文本部分
                    const buttonSvg = enableApiModeBtn.querySelector('svg');
                    enableApiModeBtn.innerHTML = '';
                    enableApiModeBtn.appendChild(buttonSvg);
                    enableApiModeBtn.appendChild(document.createTextNode(isApiModeActive ? '关闭API模式' : '启用API模式'));
                    
                    enableApiModeBtn.classList.toggle('plugin-active', isApiModeActive);
                    vscode.postMessage({ 
                        command: 'info', 
                        text: isApiModeActive ? 'API模式已开启' : 'API模式已关闭' 
                    });
                });
                
                const aiChatBtn = document.getElementById('aiChatBtn');
                let isAiChatActive = false;

                aiChatBtn.addEventListener('click', () => {
                    vscode.postMessage({ command: 'openAiChat' });
                });
                
                const enhanceBtn = document.getElementById('enhanceBtn');
                let isEnhanceActive = false;

                enhanceBtn.addEventListener('click', () => {
                    isEnhanceActive = !isEnhanceActive;
                    // 保留SVG图标，只更新文本部分
                    const buttonSvg = enhanceBtn.querySelector('svg');
                    enhanceBtn.innerHTML = '';
                    enhanceBtn.appendChild(buttonSvg);
                    enhanceBtn.appendChild(document.createTextNode(isEnhanceActive ? '关闭提示词增强' : '开启提示词增强'));
                    
                    enhanceBtn.classList.toggle('plugin-active', isEnhanceActive);
                    vscode.postMessage({ 
                        command: 'info', 
                        text: isEnhanceActive ? '提示词增强已开启' : '提示词增强已关闭' 
                    });
                });

                // 监听来自扩展的消息
                window.addEventListener('message', event => {
                    const message = event.data;
                    let btnTemp, svgTemp, cooldownTime, remainingTime, countdownInterval, tokenInfo, 
                        userInfoTemp, parts, firstFive, lastFive, now;
                    
                    switch (message.command) {
                        case 'loginComplete':
                            // 接收后端传来的用户数据
                            currentUser = message.userData;
                            
                            // 确保前端存储了密码 (即使后端传来的userData中没有)
                            if (currentUser && !currentUser.password && usernameInput.value && passwordInput.value) {
                                currentUser.password = passwordInput.value;
                                // 通知后端更新用户数据，确保密码也被保存
                                vscode.postMessage({
                                    command: 'updateUserData',
                                    userData: currentUser
                                });
                            }
                            
                            updateUIForLoginState();
                            passwordInput.value = ''; // 清除密码字段显示（但数据已保存）
                            break;
                            
                        case 'logoutComplete':
                            currentUser = null;
                            updateUIForLoginState();
                            break;
                            
                        case 'loginError':
                            loginErrorMessageDiv.textContent = message.error || '登录失败，请重试';
                            break;
                            
                        case 'tokenUpdateComplete':
                            // 更新按钮状态 - 禁用按钮并显示倒计时
                            btnTemp = document.getElementById('updateTokenBtn');
                            svgTemp = btnTemp.querySelector('svg');
                            
                            // 禁用按钮2分钟
                            btnTemp.disabled = true;
                            
                            // 设置倒计时
                            cooldownTime = 120; // 2分钟 = 120秒
                            remainingTime = cooldownTime;
                            
                            // 显示初始倒计时
                            btnTemp.innerHTML = '';
                            btnTemp.appendChild(svgTemp);
                            btnTemp.appendChild(document.createTextNode(\`冷却中(\${remainingTime}秒)\`));
                            
                            // 启动倒计时
                            countdownInterval = setInterval(() => {
                                remainingTime--;
                                
                                if (remainingTime <= 0) {
                                    // 倒计时结束，恢复按钮
                                    clearInterval(countdownInterval);
                                    btnTemp.innerHTML = '';
                                    btnTemp.appendChild(svgTemp);
                                    btnTemp.appendChild(document.createTextNode('更新令牌'));
                                    btnTemp.disabled = false;
                                } else {
                                    // 更新倒计时显示
                                    btnTemp.innerHTML = '';
                                    btnTemp.appendChild(svgTemp);
                                    btnTemp.appendChild(document.createTextNode(\`冷却中(\${remainingTime}秒)\`));
                                }
                            }, 1000);
                            
                            // 更新用户信息，添加令牌信息
                            if (currentUser) {
                                currentUser.token = message.token;
                                
                                // 只在控制台记录令牌获取成功，不在界面显示令牌信息
                                console.log('令牌获取成功，已保存在当前用户信息中');
                                
                                // 如果需要显示操作结果，可以使用vscode的通知
                                if (message.dbWriteSuccess === true) {
                                    vscode.postMessage({ 
                                        command: 'info', 
                                        text: '令牌已成功写入到Cursor数据库' 
                                    });
                                } else if (message.dbWriteSuccess === false) {
                                    vscode.postMessage({ 
                                        command: 'info', 
                                        text: '令牌获取成功' 
                                    });
                                }
                                
                                // 不再修改userInfo的HTML内容 阳阳 令牌写入失败不显示
                            }
                            break;
                            
                        case 'tokenUpdateError':
                            // 启用按钮，恢复状态
                            btnTemp = document.getElementById('updateTokenBtn');
                            svgTemp = btnTemp.querySelector('svg');
                            btnTemp.innerHTML = '';
                            btnTemp.appendChild(svgTemp);
                            btnTemp.appendChild(document.createTextNode('更新令牌'));
                            btnTemp.disabled = false;
                            break;
                            
                        case 'updateTokenUsage':
                            // 更新令牌使用情况
                            if (currentUser && message.usageInfo) {
                                // 保存使用情况信息
                                currentUser.tokenUsageInfo = message.usageInfo;
                                
                                // 更新界面
                                updateUIForLoginState();
                                
                                console.log('令牌使用情况已更新', message.usageInfo);
                            }
                            break;
                            
                        case 'pluginToggleComplete':
                            // 获取按钮和其SVG图标
                            btnTemp = document.getElementById('startPluginBtn');
                            svgTemp = btnTemp.querySelector('svg');
                            
                            // 启用按钮
                            btnTemp.disabled = false;
                            
                            // 更新按钮文本和状态
                            btnTemp.innerHTML = '';
                            btnTemp.appendChild(svgTemp);
                            
                            if (message.success) {
                                // 根据操作结果更新界面
                                // isActive为true表示已启用插件（修改了main.js文件）
                                // isActive为false表示已停用插件（从备份恢复了原始文件）
                                isPluginActive = message.isActive;
                                btnTemp.appendChild(document.createTextNode(isPluginActive ? '停用插件' : '启用插件'));
                                btnTemp.classList.toggle('plugin-active', isPluginActive);
                                
                                // 更新用户信息显示，以便根据插件状态显示或隐藏令牌使用情况
                                updateUIForLoginState();
                            } else {
                                // 操作失败，恢复原来的状态
                                btnTemp.appendChild(document.createTextNode(isPluginActive ? '停用插件' : '启用插件'));
                                btnTemp.classList.toggle('plugin-active', isPluginActive);
                                
                                // 显示错误消息
                                vscode.postMessage({ 
                                    command: 'info', 
                                    text: message.message || '操作失败' 
                                });
                            }
                            break;
                    }
                });
            }());
        </script>
    </body>
    </html>`;
}

module.exports = { getWebviewContent }; 