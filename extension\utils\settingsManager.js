const fs = require('fs');
const path = require('path');
const os = require('os');
const vscode = require('vscode');

/**
 * 获取Cursor的settings.json文件路径
 * @returns {string} settings.json的路径
 */
function getCursorSettingsPath() {
    let settingsPath = '';
    
    if (process.platform === 'win32') {
        // Windows路径
        settingsPath = path.join(process.env.APPDATA, 'Cursor', 'User', 'settings.json');
    } else if (process.platform === 'darwin') {
        // macOS路径
        const homeDir = os.homedir();
        settingsPath = path.join(homeDir, 'Library', 'Application Support', 'Cursor', 'User', 'settings.json');
    } else {
        throw new Error(`不支持的操作系统: ${process.platform}`);
    }
    
    return settingsPath;
}

/**
 * 确保settings.json中设置了"cursor.general.disableHttp2": true
 * @returns {Promise<{modified: boolean, message: string}>} 操作结果
 */
async function ensureDisableHttp2Setting() {
    try {
        // 获取settings.json文件路径
        const settingsPath = getCursorSettingsPath();
        
        // 检查文件是否存在
        if (!fs.existsSync(settingsPath)) {
            // 如果文件不存在，创建一个新文件
            fs.writeFileSync(settingsPath, '{\n  "cursor.general.disableHttp2": true\n}', 'utf8');
            return { modified: true, message: '已创建settings.json文件并设置disableHttp2为true' };
        }
        
        // 读取文件内容作为文本
        let content = fs.readFileSync(settingsPath, 'utf8');
        
        // 检查是否已经有"cursor.general.disableHttp2": true
        if (content.includes('"cursor.general.disableHttp2": true')) {
            return { modified: false, message: 'Cursor-Sun配置完成' };
        }
        
        let modified = false;
        
        // 检查是否有"cursor.general.disableHttp2": false
        if (content.includes('"cursor.general.disableHttp2": false')) {
            // 替换false为true
            content = content.replace('"cursor.general.disableHttp2": false', '"cursor.general.disableHttp2": true');
            modified = true;
        } else {
            // 在文件末尾的}前添加设置
            // 首先检查最后一个非空白字符是否是逗号
            const trimmedContent = content.trim();
            const lastNonWhitespaceChar = trimmedContent.substring(0, trimmedContent.lastIndexOf('}')).trim().slice(-1);
            
            // 如果最后一个字符不是逗号，需要添加逗号
            const needComma = lastNonWhitespaceChar !== ',' && lastNonWhitespaceChar !== '{';
            
            // 在最后的}前插入设置
            content = content.replace(/\s*}\s*$/, (match) => {
                return `${needComma ? ',' : ''}\n  "cursor.general.disableHttp2": true\n}`;
            });
            
            modified = true;
        }
        
        // 写入修改后的内容
        if (modified) {
            fs.writeFileSync(settingsPath, content, 'utf8');
            return { modified: true, message: 'Cursor-Sun配置完成' };
        }
        
        return { modified: false, message: '无需修改settings.json' };
    } catch (error) {
        throw new Error(`修改settings.json失败: ${error.message}`);
    }
}

/**
 * 检查settings.json中的disableHttp2设置是否为true
 * @returns {Promise<boolean>} 是否已设置为true
 */
async function isDisableHttp2Enabled() {
    try {
        // 获取settings.json文件路径
        const settingsPath = getCursorSettingsPath();
        
        // 检查文件是否存在
        if (!fs.existsSync(settingsPath)) {
            return false;
        }
        
        // 读取文件内容
        const content = fs.readFileSync(settingsPath, 'utf8');
        
        // 检查是否有"cursor.general.disableHttp2": true
        return content.includes('"cursor.general.disableHttp2": true');
    } catch (error) {
        console.error(`检查disableHttp2设置失败: ${error.message}`);
        return false;
    }
}

module.exports = {
    getCursorSettingsPath,
    ensureDisableHttp2Setting,
    isDisableHttp2Enabled
}; 