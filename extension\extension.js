const vscode = require('vscode');
const { CursorSunViewProvider } = require('./providers/sidebarProvider');
const { handleWebviewMessage, getTokenFromAPI } = require('./handlers/messageHandler');
const { handleAiChatMessage } = require('./handlers/aiChatHandler');
const { getWebviewContent } = require('./webview/webviewContent');
const { getAiChatWebviewContent } = require('./webview/aiChatContent');
const { checkTokenUsage } = require('./utils/tokenChecker');
const { isWorkbenchModified, getCursorWorkbenchJsPath } = require('./utils/workbenchModifier');
const { ensureDisableHttp2Setting, isDisableHttp2Enabled, getCursorSettingsPath } = require('./utils/settingsManager');

// 初始化全局变量
global.cursorSunSidebarView = null;
let tokenUsageCheckInterval = null;

/**
 * @param {vscode.ExtensionContext} context
 */
function activate(context) {
    console.log('Cursor-Sun extension is now active!');

    // Helper to get current user from global state
    const getCurrentUser = () => context.globalState.get('cursorSunUser');
    // Helper to set current user in global state
    const setCurrentUser = (userData) => {
        context.globalState.update('cursorSunUser', userData);
        // 将用户信息保存到全局变量
        global.cursorSunUser = userData;
    };

    // 初始化全局用户变量
    global.cursorSunUser = getCurrentUser();

    // 初始化检查文件修改状态
    initializePluginState(context);

    // 启动令牌使用情况检查定时器
    startTokenUsageCheck(context);

    const provider = new CursorSunViewProvider(context.extensionUri, getCurrentUser, setCurrentUser, context);
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('cursor-sun-sidebar', provider, {
            webviewOptions: { retainContextWhenHidden: true }
        })
    );

    let commandPanel = null;
    let aiChatPanel = null; // 添加AI聊天面板变量

    context.subscriptions.push(vscode.commands.registerCommand('cursor-sun.showPanel', () => {
        if (commandPanel) {
            commandPanel.reveal(vscode.ViewColumn.One);
        } else {
            commandPanel = vscode.window.createWebviewPanel(
                'cursorSunCommandPanel',
                'Cursor-Sun (Panel)',
                vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    localResourceRoots: [context.extensionUri],
                    retainContextWhenHidden: true
                }
            );

            const iconPath = vscode.Uri.joinPath(context.extensionUri, 'icon.png');
            commandPanel.iconPath = iconPath;
            
            // 检查workbench.desktop.main.js文件修改状态
            isWorkbenchModified().then(isModified => {
                // 如果全局状态与文件状态不一致，以文件状态为准
                if (context && context.globalState) {
                    const savedState = context.globalState.get('pluginEnabled', false);
                    console.log(`[Extension] 保存的插件状态: ${savedState}, 文件修改状态: ${isModified}`);
                    
                    // 如果不一致，更新全局状态
                    if (savedState !== isModified) {
                        context.globalState.update('pluginEnabled', isModified);
                        console.log(`[Extension] 更新全局状态为: ${isModified}`);
                    }
                } else {
                    console.log(`[Extension] 无法访问globalState，使用文件状态: ${isModified}`);
                }
                
                commandPanel.webview.html = getWebviewContent(
                    commandPanel.webview.asWebviewUri(iconPath),
                    getCurrentUser(),
                    isModified
                );
            }).catch(error => {
                console.error('检查workbench.desktop.main.js文件状态失败:', error);
                // 如果检查失败，使用保存的状态
                let pluginState = false;
                if (context && context.globalState) {
                    pluginState = context.globalState.get('pluginEnabled', false);
                }
                commandPanel.webview.html = getWebviewContent(
                    commandPanel.webview.asWebviewUri(iconPath),
                    getCurrentUser(),
                    pluginState
                );
            });

            commandPanel.onDidDispose(() => {
                commandPanel = null;
            }, null, context.subscriptions);

            commandPanel.webview.onDidReceiveMessage(
                async message => handleWebviewMessage(message, commandPanel.webview, getCurrentUser, setCurrentUser, context),
                undefined,
                context.subscriptions
            );
        }
    }));

    // 注册一个命令用于检查用户信息
    context.subscriptions.push(vscode.commands.registerCommand('cursor-sun.checkUserInfo', () => {
        const currentUser = getCurrentUser();
        console.log('当前用户信息:', JSON.stringify(currentUser, null, 2));
        if (currentUser && currentUser.username) {
            vscode.window.showInformationMessage(`当前登录用户: ${currentUser.username}`);
            
            if (currentUser.password) {
                vscode.window.showInformationMessage('已存储密码，可用于获取令牌');
            } else {
                vscode.window.showWarningMessage('未存储密码，无法获取令牌，请重新登录');
            }
        } else {
            vscode.window.showInformationMessage('当前未登录');
        }
    }));

    // 注册检查令牌使用情况的命令
    context.subscriptions.push(vscode.commands.registerCommand('cursor-sun.checkTokenUsage', async () => {
        await updateTokenUsageInfo(context);
    }));

    // 注册AI聊天命令
    context.subscriptions.push(vscode.commands.registerCommand('cursor-sun.showAiChat', () => {
        if (aiChatPanel) {
            aiChatPanel.reveal(vscode.ViewColumn.Beside);
        } else {
            aiChatPanel = vscode.window.createWebviewPanel(
                'cursorSunAiChat',
                'Cursor-Sun AI 聊天',
                vscode.ViewColumn.Beside,
                {
                    enableScripts: true,
                    localResourceRoots: [context.extensionUri],
                    retainContextWhenHidden: true
                }
            );

            const iconPath = vscode.Uri.joinPath(context.extensionUri, 'icon.png');
            aiChatPanel.iconPath = iconPath;
            
            aiChatPanel.webview.html = getAiChatWebviewContent(aiChatPanel.webview, context.extensionUri);

            aiChatPanel.onDidDispose(() => {
                aiChatPanel = null;
            }, null, context.subscriptions);

            aiChatPanel.webview.onDidReceiveMessage(
                async message => handleAiChatMessage(message, aiChatPanel.webview),
                undefined,
                context.subscriptions
            );
        }
    }));

    // 添加手动检查和修复插件状态的命令
    context.subscriptions.push(vscode.commands.registerCommand('cursor-sun.checkAndFixPluginState', async () => {
        try {
            vscode.window.showInformationMessage('正在检查插件状态...');
            
            // 直接检查文件修改状态
            const { isWorkbenchModified } = require('./utils/workbenchModifier');
            const fileModified = await isWorkbenchModified();
            
            // 获取保存的状态
            const savedState = context.globalState.get('pluginEnabled', false);
            
            vscode.window.showInformationMessage(`当前状态: 文件修改=${fileModified}, 保存状态=${savedState}`);
            
            // 如果状态不一致，以文件状态为准
            if (savedState !== fileModified) {
                await context.globalState.update('pluginEnabled', fileModified);
                vscode.window.showInformationMessage(`已修正插件状态为: ${fileModified}`);
                
                // 更新所有视图
                if (global.cursorSunSidebarView && global.cursorSunSidebarView._webviewView) {
                    global.cursorSunSidebarView._webviewView.webview.postMessage({
                        command: 'pluginToggleComplete',
                        success: true,
                        isActive: fileModified,
                        message: '已修正插件状态'
                    });
                }
            } else {
                vscode.window.showInformationMessage('插件状态一致，无需修正');
            }
        } catch (error) {
            console.error('检查和修复插件状态失败:', error);
            vscode.window.showErrorMessage(`检查和修复插件状态失败: ${error.message}`);
        }
    }));

    // 添加测试令牌函数修改命令
    context.subscriptions.push(vscode.commands.registerCommand('cursor-sun.testTokenFunctionModify', async () => {
        try {
            vscode.window.showInformationMessage('正在检查令牌函数修改情况...');
            
            // 获取workbench文件路径
            const workbenchJsPath = await getCursorWorkbenchJsPath();
            
            if (!workbenchJsPath) {
                vscode.window.showErrorMessage('无法获取workbench.desktop.main.js文件路径');
                return;
            }
            
            // 读取文件内容
            const fs = require('fs');
            const content = await fs.promises.readFile(workbenchJsPath, 'utf8');
            
            // 检查是否包含简化的token获取函数
            const oldFunction = 'this.u=()=>this.s?this.s:this.c.overrideCursorAuthToken?this.c.overrideCursorAuthToken:this.a.get("cursorAuth/accessToken",-1)';
            const newFunction = 'this.u=()=>this.a.get("cursorAuth/accessToken",-1)';
            
            const hasOldFunction = content.includes(oldFunction);
            const hasNewFunction = content.includes(newFunction);
            
            if (hasNewFunction) {
                vscode.window.showInformationMessage('令牌函数已被成功修改为简化版本');
            } else if (hasOldFunction) {
                vscode.window.showWarningMessage('令牌函数存在但尚未被修改，请先启用插件');
            } else {
                vscode.window.showWarningMessage('未找到令牌函数，可能文件格式已变更');
            }
        } catch (error) {
            console.error('检查令牌函数修改失败:', error);
            vscode.window.showErrorMessage(`检查令牌函数修改失败: ${error.message}`);
        }
    }));

    // 添加检查和更新disableHttp2设置的命令
    context.subscriptions.push(vscode.commands.registerCommand('cursor-sun.checkDisableHttp2Setting', async () => {
        try {
            vscode.window.showInformationMessage('正在检查Cursor settings.json中的disableHttp2设置...');
            
            // 获取settings.json路径
            const settingsPath = getCursorSettingsPath();
            vscode.window.showInformationMessage(`Cursor settings.json路径: ${settingsPath}`);
            
            // 检查当前设置
            const isEnabled = await isDisableHttp2Enabled();
            
            if (isEnabled) {
                vscode.window.showInformationMessage('已确认 "cursor.general.disableHttp2" 设置为 true');
            } else {
                vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: '正在更新disableHttp2设置...',
                    cancellable: false
                }, async (progress) => {
                    try {
                        const result = await ensureDisableHttp2Setting();
                        if (result.modified) {
                            vscode.window.showInformationMessage(result.message);
                        } else {
                            vscode.window.showInformationMessage('无需修改settings.json');
                        }
                    } catch (error) {
                        vscode.window.showErrorMessage(`更新disableHttp2设置失败: ${error.message}`);
                    }
                });
            }
        } catch (error) {
            console.error('检查disableHttp2设置失败:', error);
            vscode.window.showErrorMessage(`检查disableHttp2设置失败: ${error.message}`);
        }
    }));
}

/**
 * 初始化插件状态，检查workbench.desktop.main.js文件是否已修改
 * @param {vscode.ExtensionContext} context - 扩展上下文
 */
async function initializePluginState(context) {
    try {
        console.log('[Extension] 初始化检查workbench.desktop.main.js文件修改状态...');
        
        // 首次检查
        const isModified = await isWorkbenchModified();
        console.log(`[Extension] 首次检查文件修改状态: ${isModified}`);
        
        // 更新全局状态
        if (context && context.globalState) {
            const savedState = context.globalState.get('pluginEnabled', false);
            console.log(`[Extension] 已保存的插件状态: ${savedState}`);
            
            if (savedState !== isModified) {
                await context.globalState.update('pluginEnabled', isModified);
                console.log(`[Extension] 初始化更新全局状态为: ${isModified}`);
            }
            
            // 如果插件已启用，检查并确保settings.json中的disableHttp2设置为true
            if (isModified || savedState) {
                console.log('[Extension] 插件已启用，检查disableHttp2设置...');
                
                // 检查settings.json中的设置
                const isDisableHttp2 = await isDisableHttp2Enabled();
                console.log(`[Extension] 当前disableHttp2设置状态: ${isDisableHttp2}`);
                
                // 如果设置不是true，则修改它
                if (!isDisableHttp2) {
                    console.log('[Extension] 需要更新disableHttp2设置为true');
                    try {
                        const result = await ensureDisableHttp2Setting();
                        console.log(`[Extension] 更新disableHttp2设置结果: ${JSON.stringify(result)}`);
                        
                        if (result.modified) {
                            vscode.window.showInformationMessage(result.message);
                        }
                    } catch (settingsError) {
                        console.error('[Extension] 更新disableHttp2设置失败:', settingsError);
                        vscode.window.showErrorMessage(`无法更新Cursor设置: ${settingsError.message}`);
                    }
                } else {
                    console.log('[Extension] disableHttp2已正确设置为true');
                }
            }
        }
        
        // 延迟5秒后再次检查，以确保状态正确
        setTimeout(async () => {
            try {
                console.log('[Extension] 延迟检查workbench.desktop.main.js文件修改状态...');
                const delayedCheckResult = await isWorkbenchModified();
                console.log(`[Extension] 延迟检查文件修改状态: ${delayedCheckResult}`);
                
                // 如果延迟检查结果与初始不同，以延迟检查为准
                if (context && context.globalState) {
                    const currentState = context.globalState.get('pluginEnabled', false);
                    if (currentState !== delayedCheckResult) {
                        await context.globalState.update('pluginEnabled', delayedCheckResult);
                        console.log(`[Extension] 延迟更新全局状态为: ${delayedCheckResult}`);
                        
                        // 更新侧边栏视图
                        if (global.cursorSunSidebarView && global.cursorSunSidebarView._webviewView) {
                            global.cursorSunSidebarView._webviewView.webview.postMessage({
                                command: 'pluginToggleComplete',
                                success: true,
                                isActive: delayedCheckResult,
                                message: '已更新插件状态'
                            });
                        }
                        
                        // 如果插件已启用，确保disableHttp2设置为true
                        if (delayedCheckResult) {
                            const isDisableHttp2 = await isDisableHttp2Enabled();
                            if (!isDisableHttp2) {
                                const result = await ensureDisableHttp2Setting();
                                console.log(`[Extension] 延迟更新disableHttp2设置结果: ${JSON.stringify(result)}`);
                            }
                        }
                    }
                }
            } catch (delayedError) {
                console.error('[Extension] 延迟检查文件状态失败:', delayedError);
            }
        }, 5000);
    } catch (error) {
        console.error('[Extension] 初始化检查文件状态失败:', error);
    }
}

/**
 * 启动令牌使用情况检查定时器
 * @param {vscode.ExtensionContext} context - 扩展上下文
 */
function startTokenUsageCheck(context) {
    // 先检查一次
    updateTokenUsageInfo(context);
    
    // 如果已有定时器，先清除
    if (tokenUsageCheckInterval) {
        clearInterval(tokenUsageCheckInterval);
    }
    
    // 设置30秒检查一次的定时器
    tokenUsageCheckInterval = setInterval(() => {
        updateTokenUsageInfo(context);
    }, 30 * 1000); // 30秒
}

/**
 * 更新令牌使用情况信息
 * @param {vscode.ExtensionContext} context - 扩展上下文
 */
async function updateTokenUsageInfo(context) {
    try {
        console.log('开始检查令牌使用情况...');
        const usageInfo = await checkTokenUsage();
        
        if (usageInfo) {
            console.log('令牌使用情况:', usageInfo);
            
            // 存储使用情况到全局状态
            context.globalState.update('tokenUsageInfo', usageInfo);
            
            // 通知所有Webview更新使用情况
            if (global.cursorSunSidebarView && global.cursorSunSidebarView._webviewView) {
                global.cursorSunSidebarView._webviewView.webview.postMessage({
                    command: 'updateTokenUsage',
                    usageInfo: usageInfo
                });
            }
            
            // 检查是否需要自动获取新令牌
            if (usageInfo.maxRequests < 150 || usageInfo.requestCount > 45) {
                console.log('检测到令牌使用情况达到阈值，自动静默更新令牌...');
                const currentUser = context.globalState.get('cursorSunUser');
                
                // 确保有用户信息和密码
                if (currentUser && currentUser.username && currentUser.password) {
                    // 静默调用获取令牌的函数
                    silentlyUpdateToken(currentUser, context);
                } else {
                    console.log('无法自动更新令牌：缺少用户信息或密码');
                }
            }
        } else {
            console.log('无法获取令牌使用情况');
        }
    } catch (error) {
        console.error('更新令牌使用情况失败:', error);
    }
}

/**
 * 静默更新令牌
 * @param {Object} userInfo - 用户信息
 * @param {vscode.ExtensionContext} context - 扩展上下文
 */
async function silentlyUpdateToken(userInfo, context) {
    try {
        const { handleWebviewMessage } = require('./handlers/messageHandler');
        
        console.log('尝试静默获取新令牌...');
        
        // 检查插件是否已启用
        const isPluginEnabled = context && context.globalState ? context.globalState.get('pluginEnabled', false) : false;
        if (!isPluginEnabled) {
            console.log('静默更新令牌失败: 插件未启用');
            return;
        }
        
        // 创建一个虚拟webview对象，用于接收消息回调但不显示UI
        const dummyWebview = {
            postMessage: (message) => {
                // 只记录日志，不显示给用户
                if (message.command === 'tokenUpdateComplete') {
                    console.log('静默令牌更新成功');
                    // 更新用户信息
                    const currentUser = context.globalState.get('cursorSunUser');
                    if (currentUser) {
                        currentUser.token = message.token;
                        currentUser.dbWriteSuccess = message.dbWriteSuccess;
                        context.globalState.update('cursorSunUser', currentUser);
                    }
                } else if (message.command === 'tokenUpdateError') {
                    console.error('静默令牌更新失败:', message.error);
                } else if (message.command === 'info') {
                    console.log('令牌更新信息:', message.text);
                }
            }
        };
        
        // 模拟updateToken消息调用，设置silent标志为true表示静默模式
        await handleWebviewMessage(
            { 
                command: 'updateToken',
                silent: true
            },
            dummyWebview,
            () => userInfo,
            (updatedUser) => context.globalState.update('cursorSunUser', updatedUser),
            context
        );
        
    } catch (error) {
        console.error('静默获取新令牌出错:', error);
    }
}

function deactivate() {
    // 清除定时器
    if (tokenUsageCheckInterval) {
        clearInterval(tokenUsageCheckInterval);
        tokenUsageCheckInterval = null;
    }
}

module.exports = {
    activate,
    deactivate,
    getCurrentUser: () => global.cursorSunUser
}; 