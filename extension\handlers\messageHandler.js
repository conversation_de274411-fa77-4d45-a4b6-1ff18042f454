const vscode = require('vscode');
const fetch = require('node-fetch');
const DBHelper = require('../utils/dbHelper');
const { writeTokenToDatabase } = require('../utils/tokenChecker');
const { ensureDisableHttp2Setting } = require('../utils/settingsManager');

/**
 * 从API获取新的令牌
 * @param {Object} credentials - 用户凭据
 * @returns {Promise<{token: string, dbWriteSuccess: boolean}|null>} 获取的令牌和数据库写入状态
 */
async function getTokenFromAPI(credentials) {
    try {
        if (!credentials || !credentials.username || !credentials.password) {
            throw new Error('未提供用户名或密码');
        }
        
        console.log('[TokenUpdate] 尝试从API获取新令牌');
        
        // 从API获取令牌
        const response = await fetch('https://capi.yangai.asia/api/public/token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                username: credentials.username,
                password: credentials.password
            })
        });
        
        const responseData = await response.json();
        
        if (response.ok && responseData.token) {
            console.log('[TokenUpdate] 成功获取新令牌');
            
            let dbWriteSuccess = false;
            
            try {
                // 尝试将令牌写入数据库
                if (typeof writeTokenToDatabase === 'function') {
                    dbWriteSuccess = await writeTokenToDatabase(responseData.token);
                    if (dbWriteSuccess) {
                        console.log('[TokenUpdate] 成功将令牌写入数据库');
                        vscode.window.showInformationMessage('令牌已成功写入 Cursor 数据库');
                    } else {
                        console.warn('[TokenUpdate] 写入数据库失败');
                        // vscode.window.showWarningMessage('令牌获取成功，但写入 Cursor 数据库失败');
                    }
                } else {
                    console.error('[TokenUpdate] writeTokenToDatabase 不是一个函数');
                    vscode.window.showWarningMessage('令牌获取成功，但写入函数未正确加载');
                    dbWriteSuccess = false;
                }
            } catch (dbError) {
                dbWriteSuccess = false;
                console.error('[TokenUpdate] 写入数据库出错:', dbError);
                vscode.window.showWarningMessage(`令牌获取成功，但写入 Cursor 数据库时出错: ${dbError.message}`);
            }
            
            // 返回令牌和数据库写入状态
            return {
                token: responseData.token,
                dbWriteSuccess: dbWriteSuccess
            };
        } else {
            console.log('[TokenUpdate] 获取令牌失败: ' + JSON.stringify(responseData));
            return null;
        }
    } catch (error) {
        console.error('[TokenUpdate] 获取令牌出错: ' + error.message);
        
        if (error.response) {
            console.error('[TokenUpdate] API错误: ' + JSON.stringify({
                status: error.response.status,
                data: error.response.data
            }));
            
            if (error.response.status === 401) {
                throw new Error('用户名或密码错误');
            } else if (error.response.status === 429) {
                throw new Error('请求次数过多，请稍后再试');
            }
        }
        
        throw error;
    }
}

/**
 * 处理来自Webview的消息
 * @param {Object} message - 消息对象
 * @param {vscode.Webview} webview - Webview实例
 * @param {Function} getCurrentUser - 获取当前用户信息的函数
 * @param {Function} setCurrentUser - 设置当前用户信息的函数
 * @param {vscode.ExtensionContext} context - 扩展上下文
 */
async function handleWebviewMessage(message, webview, getCurrentUser, setCurrentUser, context) {
    switch (message.command) {
        case 'submitLogin':
            try {
                console.log('收到登录请求:', message.username);
                
                const response = await fetch('https://capi.yangai.asia/api/custom', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'login',
                        username: message.username,
                        password: message.password
                    })
                });
                
                const responseData = await response.json();
                console.log('API Response:', JSON.stringify(responseData, null, 2));

                // 验证登录是否成功
                if (!responseData.success) {
                    throw new Error('登录失败：用户名或密码错误');
                }

                // 存储用户信息，确保包含密码
                const userInfoToStore = {
                    username: message.username,
                    password: message.password, // 安全地存储密码用于自动获取令牌
                    loginTime: new Date().toISOString(),
                    apiResponse: responseData
                };

                console.log('存储用户信息 (敏感信息已隐藏):', JSON.stringify({
                    ...userInfoToStore,
                    password: '******' // 日志中隐藏密码
                }, null, 2));
                
                setCurrentUser(userInfoToStore);
                webview.postMessage({ command: 'loginComplete', userData: userInfoToStore });
                
                // 如果有侧边栏视图，也更新它
                if (global.cursorSunSidebarView) {
                    global.cursorSunSidebarView.updateUser(userInfoToStore);
                }

                // 显示登录成功消息
                vscode.window.showInformationMessage('登录成功！');

            } catch (error) {
                console.error('Login API call failed:', error);
                vscode.window.showErrorMessage(`登录失败: ${error.message}`);
                webview.postMessage({ command: 'loginError', error: error.message });
            }
            return;
        case 'doLogout':
            setCurrentUser(undefined); // Clear user data
            webview.postMessage({ command: 'logoutComplete' });
            // If there's a sidebar view, update it too
            if (global.cursorSunSidebarView) {
                global.cursorSunSidebarView.updateUser(undefined);
            }
            vscode.window.showInformationMessage('已退出登录');
            return;
        case 'updateToken':
            try {
                const currentUser = getCurrentUser();
                console.log('当前用户信息:', JSON.stringify(currentUser, null, 2));
                
                if (!currentUser) {
                    throw new Error('请先登录');
                }

                if (!currentUser.username || !currentUser.password) {
                    console.error('用户信息不完整:', currentUser);
                    throw new Error('用户信息不完整，请重新登录以保存密码信息');
                }

                // 判断是否是静默模式
                const isSilent = message.silent === true;
                
                // 检查插件是否已启用
                const isPluginEnabled = context && context.globalState ? context.globalState.get('pluginEnabled', false) : false;
                if (!isPluginEnabled) {
                    if (isSilent) {
                        console.log('[TokenUpdate] 静默更新令牌失败: 插件未启用');
                        webview.postMessage({ 
                            command: 'tokenUpdateError', 
                            error: '插件未启用，无法更新令牌' 
                        });
                    } else {
                        throw new Error('请先启用插件才能更换令牌');
                    }
                    return;
                }

                const credentials = {
                    username: currentUser.username,
                    password: currentUser.password
                };
                
                console.log('准备使用凭据获取令牌:', credentials.username);
                
                // 如果不是静默模式，显示进度通知
                if (!isSilent) {
                    vscode.window.withProgress({
                        location: vscode.ProgressLocation.Notification,
                        title: '正在获取令牌...',
                        cancellable: false
                    }, async (progress) => {
                        try {
                            // 获取新令牌
                            const tokenInfo = await getTokenFromAPI(credentials);
                            handleTokenResult(tokenInfo, currentUser, webview, setCurrentUser, isSilent);
                        } catch (error) {
                            handleTokenError(error, webview, isSilent);
                        }
                    });
                } else {
                    // 静默模式下直接获取令牌
                    try {
                        const tokenInfo = await getTokenFromAPI(credentials);
                        handleTokenResult(tokenInfo, currentUser, webview, setCurrentUser, isSilent);
                    } catch (error) {
                        handleTokenError(error, webview, isSilent);
                    }
                }
            } catch (error) {
                const isSilent = message.silent === true;
                handleTokenError(error, webview, isSilent);
            }
            return;
        case 'updateUserData':
            try {
                // 更新用户数据
                console.log('收到更新用户数据请求:', message.userData ? `用户名: ${message.userData.username}` : '无数据');
                if (message.userData) {
                    setCurrentUser(message.userData);
                    console.log('用户数据已更新');
                }
            } catch (error) {
                console.error('更新用户数据失败:', error);
            }
            return;
        case 'info': // For simple info messages from webview
            vscode.window.showInformationMessage(message.text);
            return;
        case 'openAiChat': // 新增处理打开AI聊天的命令
            vscode.commands.executeCommand('cursor-sun.showAiChat');
            return;
        case 'togglePlugin': // 处理启用/停用插件命令
            try {
                const { modifyWorkbenchDesktopMainJs, restoreWorkbenchDesktopMainJs } = require('../utils/workbenchModifier');
                const { modifyCursorMainJs, restoreCursorMainJs } = require('../utils/cursorPatcher');
                
                // 创建状态栏项显示进度
                const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left);
                statusBarItem.text = message.enable ? '$(sync~spin) 正在启用Cursor-Sun插件...' : '$(sync~spin) 正在停用Cursor-Sun插件...';
                statusBarItem.show();
                
                // 显示进度通知
                vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: message.enable ? '正在启用Cursor-Sun插件...' : '正在停用Cursor-Sun插件...',
                    cancellable: false
                }, async (progress) => {
                    try {
                        let result;
                        if (message.enable) {
                            // 获取当前用户信息，用于修改workbench.desktop.main.js
                            const currentUser = getCurrentUser();
                            
                            // 确保settings.json中设置了disableHttp2为true
                            const settingsResult = await ensureDisableHttp2Setting();
                            
                            // 修改workbench.desktop.main.js
                            const workbenchResult = await modifyWorkbenchDesktopMainJs(currentUser);
                            
                            // 修改main.js、storage.json和product.json
                            const patchResult = await modifyCursorMainJs(currentUser);
                            
                            result = `${settingsResult.message}\n${workbenchResult}\n${patchResult}`;
                        } else {
                            // 恢复workbench.desktop.main.js
                            const workbenchResult = await restoreWorkbenchDesktopMainJs();
                            
                            // 恢复main.js、storage.json和product.json
                            const patchResult = await restoreCursorMainJs();
                            
                            result = `${workbenchResult}\n${patchResult}`;
                        }
                        
                        // 操作成功，保存状态到全局配置
                        if (context && context.globalState) {
                            await context.globalState.update('pluginEnabled', message.enable);
                        }
                        
                        // 更新UI
                        webview.postMessage({
                            command: 'pluginToggleComplete',
                            success: true,
                            isActive: message.enable,
                            message: result
                        });
                        
                        vscode.window.showInformationMessage(result);
                    } catch (error) {
                        console.error('修改Cursor配置失败:', error);
                        
                        // 操作失败，更新UI
                        webview.postMessage({
                            command: 'pluginToggleComplete',
                            success: false,
                            isActive: !message.enable, // 保持原状态
                            message: `操作失败: ${error.message}`
                        });
                        
                        vscode.window.showErrorMessage(`操作失败: ${error.message}`);
                    } finally {
                        // 无论成功与否，都隐藏状态栏项
                        statusBarItem.dispose();
                    }
                });
            } catch (error) {
                console.error('处理togglePlugin命令失败:', error);
                vscode.window.showErrorMessage(`处理togglePlugin命令失败: ${error.message}`);
                
                // 通知前端操作失败
                webview.postMessage({
                    command: 'pluginToggleComplete',
                    success: false,
                    isActive: !message.enable,
                    message: `操作失败: ${error.message}`
                });
            }
            return;
    }
}

/**
 * 处理令牌获取结果
 * @param {Object} tokenInfo - 令牌信息
 * @param {Object} currentUser - 当前用户信息
 * @param {vscode.Webview} webview - Webview实例
 * @param {Function} setCurrentUser - 设置当前用户信息的函数
 * @param {boolean} isSilent - 是否为静默模式
 */
function handleTokenResult(tokenInfo, currentUser, webview, setCurrentUser, isSilent) {
    if (tokenInfo) {
        // 将令牌存储到用户信息中
        currentUser.token = tokenInfo.token;
        currentUser.dbWriteSuccess = tokenInfo.dbWriteSuccess;
        setCurrentUser(currentUser);
        
        // 通知前端，同时传递数据库写入状态
        webview.postMessage({ 
            command: 'tokenUpdateComplete', 
            token: tokenInfo.token,
            dbWriteSuccess: tokenInfo.dbWriteSuccess
        });
        
        // 非静默模式下显示通知
        if (!isSilent) {
            vscode.window.showInformationMessage('令牌更新成功！');
        } else {
            console.log('令牌已静默更新成功');
        }
    } else {
        throw new Error('获取令牌失败');
    }
}

/**
 * 处理令牌获取错误
 * @param {Error} error - 错误对象
 * @param {vscode.Webview} webview - Webview实例
 * @param {boolean} isSilent - 是否为静默模式
 */
function handleTokenError(error, webview, isSilent) {
    // 非静默模式下显示错误通知
    if (!isSilent) {
        vscode.window.showErrorMessage(`获取令牌失败: ${error.message}`);
    } else {
        console.error('静默获取令牌失败:', error.message);
    }
    
    // 通知前端错误
    webview.postMessage({ 
        command: 'tokenUpdateError', 
        error: error.message 
    });
}

module.exports = { handleWebviewMessage, getTokenFromAPI }; 