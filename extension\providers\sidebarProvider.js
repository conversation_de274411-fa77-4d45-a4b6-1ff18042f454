const vscode = require('vscode');
const { getWebviewContent } = require('../webview/webviewContent');
const { isWorkbenchModified } = require('../utils/workbenchModifier');

/**
 * 侧边栏视图提供者类
 */
class CursorSunViewProvider {
    /**
     * 当前视图实例的静态引用
     * @type {CursorSunViewProvider}
     */
    static currentView = null;

    /**
     * @param {vscode.Uri} extensionUri - 扩展URI
     * @param {Function} getCurrentUser - 获取当前用户信息的函数
     * @param {Function} setCurrentUser - 设置当前用户信息的函数
     * @param {vscode.ExtensionContext} context - 扩展上下文
     */
    constructor(extensionUri, getCurrentUser, setCurrentUser, context) {
        this.extensionUri = extensionUri;
        this.getCurrentUser = getCurrentUser;
        this.setCurrentUser = setCurrentUser;
        this.context = context; // 保存context引用
        this._webviewView = null;
    }

    /**
     * 解析Webview视图
     * @param {vscode.WebviewView} webviewView - Webview视图实例
     * @param {vscode.WebviewViewResolveContext} context - 解析上下文
     * @param {vscode.CancellationToken} token - 取消令牌
     */
    resolveWebviewView(webviewView, context, token) {
        CursorSunViewProvider.currentView = this; // 设置静态引用
        global.cursorSunSidebarView = this; // 全局引用，用于消息处理
        this._webviewView = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.extensionUri]
        };

        const iconPath = vscode.Uri.joinPath(this.extensionUri, 'icon.png');
        const iconSrc = webviewView.webview.asWebviewUri(iconPath);

        // 检查workbench.desktop.main.js文件修改状态
        isWorkbenchModified().then(isModified => {
            // 检查扩展上下文是否可用
            if (this.context && this.context.globalState) {
                // 如果全局状态与文件状态不一致，以文件状态为准
                const savedState = this.context.globalState.get('pluginEnabled', false);
                console.log(`[SidebarProvider] 保存的插件状态: ${savedState}, 文件修改状态: ${isModified}`);
                
                // 如果不一致，更新全局状态
                if (savedState !== isModified) {
                    this.context.globalState.update('pluginEnabled', isModified);
                    console.log(`[SidebarProvider] 更新全局状态为: ${isModified}`);
                }
                
                webviewView.webview.html = getWebviewContent(iconSrc, this.getCurrentUser(), isModified);
            } else {
                // 如果无法访问全局状态，则直接使用文件检查结果
                console.log(`[SidebarProvider] 无法访问globalState，使用文件状态: ${isModified}`);
                webviewView.webview.html = getWebviewContent(iconSrc, this.getCurrentUser(), isModified);
            }
        }).catch(error => {
            console.error('检查workbench.desktop.main.js文件状态失败:', error);
            // 如果检查失败并且上下文可用，使用保存的状态
            if (this.context && this.context.globalState) {
                const savedState = this.context.globalState.get('pluginEnabled', false);
                webviewView.webview.html = getWebviewContent(iconSrc, this.getCurrentUser(), savedState);
            } else {
                // 如果无法访问上下文，使用默认值false
                webviewView.webview.html = getWebviewContent(iconSrc, this.getCurrentUser(), false);
            }
        });

        // 导入消息处理函数
        const { handleWebviewMessage } = require('../handlers/messageHandler');

        webviewView.webview.onDidReceiveMessage(
            async message => handleWebviewMessage(message, webviewView.webview, this.getCurrentUser, this.setCurrentUser, this.context)
        );
        
        // 处理视图销毁
        token.onCancellationRequested(() => {
            if (CursorSunViewProvider.currentView === this) {
                CursorSunViewProvider.currentView = null;
                global.cursorSunSidebarView = null;
            }
        });
    }

    /**
     * 更新Webview的用户信息
     * @param {Object} userData - 用户数据
     */
    updateUser(userData) {
        if (this._webviewView) {
            if (userData) {
                 this._webviewView.webview.postMessage({ command: 'loginComplete', userData });
            } else {
                 this._webviewView.webview.postMessage({ command: 'logoutComplete' });
            }
        }
    }
}

module.exports = { CursorSunViewProvider }; 