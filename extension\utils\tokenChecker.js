const https = require('https');
const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');
const { exec } = require('child_process');
const fs = require('fs');

/**
 * 获取 state.vscdb 文件路径
 * @returns {Promise<string>} 数据库文件路径
 */
async function getStateVscdbPath() {
    return new Promise((resolve, reject) => {
        const platform = os.platform();
        
        if (platform === 'win32') {
            // Windows 路径
            const appData = process.env.APPDATA;
            if (!appData) {
                return reject(new Error('无法获取 APPDATA 路径'));
            }
            
            const dbPath = path.join(appData, 'Cursor', 'User', 'globalStorage', 'state.vscdb');
            
            // 检查文件是否存在
            fs.access(dbPath, fs.constants.F_OK, (err) => {
                if (err) {
                    console.error(`数据库文件不存在: ${dbPath}`);
                    return reject(new Error(`数据库文件不存在: ${dbPath}`));
                }
                resolve(dbPath);
            });
        } else if (platform === 'darwin') {
            // macOS 路径
            // 获取当前用户名
            exec('whoami', (error, stdout, stderr) => {
                if (error) {
                    console.error(`获取用户名出错: ${error.message}`);
                    return reject(new Error(`获取用户名出错: ${error.message}`));
                }
                
                const username = stdout.trim();
                const dbPath = path.join('/Users', username, 'Library/Application Support/Cursor/User/globalStorage/state.vscdb');
                
                // 检查文件是否存在
                fs.access(dbPath, fs.constants.F_OK, (err) => {
                    if (err) {
                        console.error(`数据库文件不存在: ${dbPath}`);
                        return reject(new Error(`数据库文件不存在: ${dbPath}`));
                    }
                    resolve(dbPath);
                });
            });
        } else {
            reject(new Error(`不支持的操作系统: ${platform}`));
        }
    });
}

/**
 * 生成随机邮箱地址
 * @returns {string} 随机邮箱地址
 */
function generateRandomEmail() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let email = '';
    
    // 生成8-12位随机字符
    const length = Math.floor(Math.random() * 5) + 8;
    for (let i = 0; i < length; i++) {
        email += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    // 添加域名后缀
    const domains = ['gmail.com', 'outlook.com', 'yahoo.com', 'hotmail.com', 'icloud.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    
    return `${email}@${domain}`;
}

/**
 * 将令牌写入 state.vscdb 数据库
 * @param {string} token - 要写入的令牌
 * @returns {Promise<boolean>} 操作是否成功
 */
async function writeTokenToDatabase(token) {
    try {
        // 获取数据库路径
        const dbPath = await getStateVscdbPath();
        console.log(`数据库路径: ${dbPath}`);
        
        // 生成随机邮箱
        const randomEmail = generateRandomEmail();
        
        // 打开数据库连接
        const db = new Database(dbPath);
        
        try {
            // 开始事务
            db.prepare('BEGIN TRANSACTION').run();
            
            // 更新 refreshToken
            const refreshStmt = db.prepare('INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)');
            refreshStmt.run('cursorAuth/refreshToken', token);
            console.log('更新 refreshToken 成功');
            
            // 更新 accessToken
            const accessStmt = db.prepare('INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)');
            accessStmt.run('cursorAuth/accessToken', token);
            console.log('更新 accessToken 成功');
            
            // 更新 cachedEmail
            const emailStmt = db.prepare('INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)');
            emailStmt.run('cursorAuth/cachedEmail', randomEmail);
            console.log('更新 cachedEmail 成功');
            
            // 提交事务
            db.prepare('COMMIT').run();
            console.log('所有更新已提交');
            
            return true;
        } catch (error) {
            // 如果发生错误，回滚事务
            db.prepare('ROLLBACK').run();
            throw error;
        } finally {
            // 关闭数据库连接
            db.close();
            console.log('数据库连接已关闭');
        }
    } catch (error) {
        console.error(`数据库操作失败: ${error.message}`);
        return false;
    }
}

/**
 * 检查令牌使用情况
 * @returns {Promise<string|null>} 令牌或null
 */
async function checkTokenUsage() {
    try {
        // 获取数据库路径
        const dbPath = await getStateVscdbPath();
        console.log(`数据库路径: ${dbPath}`);
        
        // 打开数据库连接
        const db = new Database(dbPath);
        
        try {
            // 查询accessToken
            const stmt = db.prepare('SELECT value FROM ItemTable WHERE key = ?');
            const row = stmt.get('cursorAuth/accessToken');
            
            if (!row) {
                console.log('未找到令牌');
                return null;
            }
            
            const token = row.value;
            console.log('成功获取令牌');
            
            return token;
        } finally {
            // 关闭数据库连接
            db.close();
            console.log('数据库连接已关闭');
        }
    } catch (error) {
        console.error(`检查令牌失败: ${error.message}`);
        return null;
    }
}

module.exports = {
    checkTokenUsage: checkTokenUsage,
    getStateVscdbPath: getStateVscdbPath,
    writeTokenToDatabase: writeTokenToDatabase
}; 