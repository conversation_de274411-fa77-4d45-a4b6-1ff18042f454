const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');
const vscode = require('vscode');

/**
 * 获取Cursor的product.json文件路径
 * @returns {string|null} product.json的路径，失败返回null
 */
function getCursorProductJsonPath() {
    try {
        let productJsonPath = '';
        
        if (process.platform === 'win32') {
            // Windows路径
            const userProfile = process.env.USERPROFILE;
            if (!userProfile) {
                console.log('[ProductModifier] 无法获取用户配置文件路径');
                return null;
            }
            productJsonPath = path.join(userProfile, 'AppData', 'Local', 'Programs', 'cursor', 'resources', 'app', 'product.json');
        } else if (process.platform === 'darwin') {
            // macOS路径
            const homeDir = os.homedir();
            productJsonPath = path.join(homeDir, 'Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'product.json');
            
            // 如果上面的路径不存在，尝试系统级应用程序目录
            if (!fs.existsSync(productJsonPath)) {
                productJsonPath = path.join('/Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'product.json');
            }
        } else {
            console.log(`[ProductModifier] 不支持的操作系统: ${process.platform}`);
            return null;
        }
        
        // 检查文件是否存在
        if (!fs.existsSync(productJsonPath)) {
            console.log(`[ProductModifier] 找不到product.json文件: ${productJsonPath}`);
            return null;
        }
        
        return productJsonPath;
    } catch (error) {
        console.log('[ProductModifier] 获取product.json路径失败: ' + error.message);
        return null;
    }
}

/**
 * 检查文件是否可写
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} 文件是否可写
 */
async function isFileWritable(filePath) {
    try {
        // 检查文件是否存在
        await fs.promises.access(filePath, fs.constants.F_OK);
        
        // 尝试打开文件进行写入测试
        const fd = await fs.promises.open(filePath, 'r+');
        await fd.close();
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 修改product.json文件
 * @returns {Promise<boolean>} 是否成功
 */
async function modifyProductJson() {
    try {
        // 获取product.json文件路径
        const productJsonPath = getCursorProductJsonPath();
        if (!productJsonPath) {
            console.log('[ProductModifier] 获取product.json路径失败');
            return false;
        }
        
        console.log(`[ProductModifier] product.json路径: ${productJsonPath}`);
        
        // 获取原始文件信息
        const stats = fs.statSync(productJsonPath);
        const originalMode = stats.mode;
        
        // 检查文件是否可写
        const writable = await isFileWritable(productJsonPath);
        
        // 如果不可写，尝试移除文件的只读属性
        if (!writable) {
            try {
                if (process.platform === 'win32') {
                    execSync(`attrib -R "${productJsonPath}"`);
                } else {
                    fs.chmodSync(productJsonPath, 0o666);
                }
            } catch (error) {
                console.log(`[ProductModifier] 警告: 修改文件权限失败: ${error.message}`);
                // 继续尝试修改文件
            }
        }
        
        // 备份文件
        const backupPath = productJsonPath + '.backup';
        if (!fs.existsSync(backupPath)) {
            fs.copyFileSync(productJsonPath, backupPath);
            console.log(`[ProductModifier] 已创建备份: ${backupPath}`);
        }
        
        // 读取原始文件内容
        const content = fs.readFileSync(productJsonPath, 'utf8');
        
        // 按行分割内容
        const lines = content.split(/\r?\n/);
        const newLines = [];
        
        // 移除包含vs/workbench/workbench.desktop.main.js的行
        for (const line of lines) {
            if (!line.includes('vs/workbench/workbench.desktop.main.js')) {
                newLines.push(line);
            } else {
                console.log(`[ProductModifier] 移除行: ${line.trim()}`);
            }
        }
        
        // 重新组合内容
        const newContent = newLines.join('\n');
        
        // 写入修改后的内容
        fs.writeFileSync(productJsonPath, newContent, { mode: originalMode });
        
        console.log('[ProductModifier] product.json文件更新成功');
        return true;
    } catch (error) {
        console.log('[ProductModifier] 修改product.json文件失败: ' + error.message);
        if (error.stack) {
            console.log('[ProductModifier] 错误堆栈: ' + error.stack);
        }
        return false;
    }
}

/**
 * 恢复product.json文件
 * @returns {Promise<boolean>} 是否成功
 */
async function restoreProductJson() {
    try {
        // 获取product.json文件路径
        const productJsonPath = getCursorProductJsonPath();
        if (!productJsonPath) {
            console.log('[ProductModifier] 获取product.json路径失败');
            return false;
        }
        
        // 备份文件路径
        const backupPath = productJsonPath + '.backup';
        
        // 检查备份文件是否存在
        if (!fs.existsSync(backupPath)) {
            console.log('[ProductModifier] 备份文件不存在，无法恢复');
            return false;
        }
        
        // 检查文件是否可写
        const writable = await isFileWritable(productJsonPath);
        
        // 如果不可写，尝试移除文件的只读属性
        if (!writable) {
            try {
                if (process.platform === 'win32') {
                    execSync(`attrib -R "${productJsonPath}"`);
                } else {
                    fs.chmodSync(productJsonPath, 0o666);
                }
            } catch (error) {
                console.log(`[ProductModifier] 警告: 修改文件权限失败: ${error.message}`);
                // 继续尝试修改文件
            }
        }
        
        // 从备份恢复文件
        fs.copyFileSync(backupPath, productJsonPath);
        
        console.log('[ProductModifier] product.json文件已恢复');
        return true;
    } catch (error) {
        console.log('[ProductModifier] 恢复product.json文件失败: ' + error.message);
        return false;
    }
}

module.exports = {
    modifyProductJson,
    restoreProductJson
}; 