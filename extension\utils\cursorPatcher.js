const fs = require('fs');
const path = require('path');
const os = require('os');
const vscode = require('vscode');
const { elevateAndModify, isFileWritable } = require('./elevatePermissions');
const { modifyStorageJson, restoreStorageJson } = require('./storageModifier');
const { modifyProductJson, restoreProductJson } = require('./productModifier');
const { modifyWorkbenchDesktopMainJs, restoreWorkbenchDesktopMainJs } = require('./workbenchModifier');

// 备份文件后缀
const BACKUP_SUFFIX = '.backup';

/**
 * 获取Cursor的main.js文件路径
 * @returns {Promise<string>} main.js的路径
 */
async function getCursorMainJsPath() {
    let mainJsPath = '';
    
    if (process.platform === 'win32') {
        // Windows路径
        const userProfile = process.env.USERPROFILE;
        if (!userProfile) {
            throw new Error('无法获取用户配置文件路径');
        }
        mainJsPath = path.join(userProfile, 'AppData', 'Local', 'Programs', 'cursor', 'resources', 'app', 'out', 'main.js');
    } else if (process.platform === 'darwin') {
        // macOS路径
        const homeDir = os.homedir();
        mainJsPath = path.join(homeDir, 'Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'out', 'main.js');
        
        // 如果上面的路径不存在，尝试系统级应用程序目录
        if (!fs.existsSync(mainJsPath)) {
            mainJsPath = path.join('/Applications', 'Cursor.app', 'Contents', 'Resources', 'app', 'out', 'main.js');
        }
    } else {
        throw new Error(`不支持的操作系统: ${process.platform}`);
    }
    
    // 检查文件是否存在
    if (!fs.existsSync(mainJsPath)) {
        throw new Error(`找不到main.js文件: ${mainJsPath}`);
    }
    
    return mainJsPath;
}

/**
 * 替换字符串中的正则表达式匹配部分
 * @param {string} content - 原始内容
 * @param {string} pattern - 正则表达式模式
 * @param {string} replacement - 替换文本
 * @returns {string} 替换后的内容
 */
function replacePattern(content, pattern, replacement) {
    const regex = new RegExp(pattern);
    return content.replace(regex, replacement);
}

/**
 * 修改Cursor的main.js文件
 * @param {Object} currentUser - 当前用户信息
 * @returns {Promise<string>} 成功消息或错误信息
 */
async function modifyCursorMainJs(currentUser) {
    try {
        // 获取main.js文件路径
        const mainJsPath = await getCursorMainJsPath();
        
        // 检查文件是否可写
        const writable = await isFileWritable(mainJsPath);
        
        // 如果不可写，尝试提升权限
        if (!writable) {
            console.log('文件不可写，尝试提升权限');
            const result = await elevateAndModify('modify', mainJsPath);
            if (result) {
                // 如果成功提权修改main.js，再修改storage.json和product.json
                const storageResult = await modifyStorageJson();
                const productResult = await modifyProductJson();
                
                if (!storageResult || !productResult) {
                    console.log('修改某些文件失败，但main.js已修改成功');
                }
                
                let message = '插件启用成功';
                // if (storageResult) message += '、storage.json';
                // if (productResult) message += '、product.json';
                
                return message;
            }
        }
        
        // 如果文件可写或提权不返回结果，按照标准方式处理
        // 获取原始文件信息
        const stats = fs.statSync(mainJsPath);
        const originalMode = stats.mode;
        
        // 读取原始文件内容
        let content = fs.readFileSync(mainJsPath, 'utf8');
        
        // 创建备份文件
        const backupPath = mainJsPath + BACKUP_SUFFIX;
        if (!fs.existsSync(backupPath)) {
            fs.writeFileSync(backupPath, content, { mode: originalMode });
        }
        
        // 执行替换
        // 替换getMachineId函数
        content = replacePattern(
            content, 
            `async getMachineId\\(\\)\\{return [^??]+\\?\\?([^}]+)\\}`,
            `async getMachineId(){return $1}`
        );
        
        // 替换getMacMachineId函数
        content = replacePattern(
            content,
            `async getMacMachineId\\(\\)\\{return [^??]+\\?\\?([^}]+)\\}`,
            `async getMacMachineId(){return $1}`
        );
        
        // 写入修改后的内容
        fs.writeFileSync(mainJsPath, content, { mode: originalMode });
        
        // 修改storage.json和product.json文件
        const storageResult = await modifyStorageJson();
        const productResult = await modifyProductJson();
        
        let message = '插件启用成功';
        // if (storageResult) message += '、storage.json';
        // if (productResult) message += '、product.json';
        
        return message;
    } catch (error) {
        throw new Error(`修改Cursor main.js失败: ${error.message}`);
    }
}

/**
 * 恢复Cursor的main.js文件
 * @returns {Promise<string>} 成功消息或错误信息
 */
async function restoreCursorMainJs() {
    try {
        // 获取main.js文件路径
        const mainJsPath = await getCursorMainJsPath();
        
        // 备份文件路径
        const backupPath = mainJsPath + BACKUP_SUFFIX;
        
        // 检查备份文件是否存在
        if (!fs.existsSync(backupPath)) {
            throw new Error('未找到备份文件，无法恢复');
        }
        
        // 检查文件是否可写
        const writable = await isFileWritable(mainJsPath);
        
        // 如果不可写，尝试提升权限
        if (!writable) {
            console.log('文件不可写，尝试提升权限');
            const result = await elevateAndModify('restore', mainJsPath);
            if (result) {
                // 如果成功提权恢复main.js，再恢复storage.json和product.json
                const storageResult = await restoreStorageJson();
                const productResult = await restoreProductJson();
                
                if (!storageResult || !productResult) {
                    console.log('恢复某些文件失败，但main.js已恢复成功');
                }
                
                let message = '成功恢复Cursor文件';
                if (storageResult) message += '、storage.json';
                if (productResult) message += '、product.json';
                
                return message;
            }
        }
        
        // 如果文件可写或提权不返回结果，按照标准方式处理
        // 获取原始文件信息
        const stats = fs.statSync(backupPath);
        const originalMode = stats.mode;
        
        // 读取备份文件内容
        const content = fs.readFileSync(backupPath, 'utf8');
        
        // 写回原始文件
        fs.writeFileSync(mainJsPath, content, { mode: originalMode });
        
        // 恢复storage.json和product.json文件
        const storageResult = await restoreStorageJson();
        const productResult = await restoreProductJson();
        
        let message = '成功恢复文件';
        // if (storageResult) message += '、storage.json';
        // if (productResult) message += '、product.json';
        
        return message;
    } catch (error) {
        throw new Error(`恢复Cursor main.js失败: ${error.message}`);
    }
}

module.exports = {
    modifyCursorMainJs,
    restoreCursorMainJs
}; 