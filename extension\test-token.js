const https = require('https');

/**
 * 解析JWT令牌并提取用户ID
 * @param {string} token - JWT令牌
 * @returns {string} - 提取的用户ID
 */
function decodeJwtToken(token) {
  try {
    // 将JWT令牌分割为三部分
    const parts = token.split('.');
    
    if (parts.length !== 3) {
      console.error('无效的JWT令牌格式');
      return '';
    }
    
    // 解码JWT的payload部分（第二部分）
    const payload = parts[1];
    const decodedPayload = Buffer.from(payload, 'base64').toString('utf-8');
    
    // 解析JSON
    const payloadObj = JSON.parse(decodedPayload);
    
    // 提取sub字段中的用户ID部分
    if (payloadObj.sub && payloadObj.sub.includes('|')) {
      const userId = payloadObj.sub.split('|')[1];
      return userId;
    } else {
      console.error('无法从令牌中提取用户ID');
      return '';
    }
  } catch (error) {
    console.error('解析JWT令牌失败:', error.message);
    return '';
  }
}

/**
 * 获取Cursor API使用情况
 * @param {string} token - JWT令牌
 * @returns {Promise<Object>} - 使用情况数据
 */
async function getGpt4Usage(token) {
  try {
    // 提取用户ID
    const userId = decodeJwtToken(token);
    // 创建用户ID和令牌的组合字符串
    const userIdWithToken = userId + "%3A%3A" + token;

    // 设置请求选项
    const options = {
      hostname: 'www.cursor.com',
      path: '/api/usage',
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Cache-Control': 'no-cache',
        'Cookie': `WorkosCursorSessionToken=${userIdWithToken}`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36'
      }
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const responseData = data ? JSON.parse(data) : {};
            resolve(responseData);
          } catch (error) {
            console.log('解析响应失败:', error.message);
            console.log('原始响应:', data);
            resolve({});
          }
        });
      });
      
      req.on('error', (error) => {
        console.error('请求失败:', error.message);
        reject(error);
      });
      
      req.end();
    });
  } catch (error) {
    console.error('执行失败:', error.message);
    return {};
  }
}

/**
 * 主函数：测试单个token的使用情况
 */
async function testToken() {
  // 检查是否提供了token作为命令行参数
  const token = process.argv[2];
  
  if (!token) {
    console.error('请提供token作为命令行参数');
    console.log('用法: node test-token.js <your_token>');
    return;
  }
  
  console.log('正在获取token使用情况...');
  
  try {
    const usageData = await getGpt4Usage(token);
    
    if (usageData && usageData['gpt-4']) {
      const gpt4Data = usageData['gpt-4'];
      
      // 翻译成中文并显示
      const translatedData = {
        '请求次数': gpt4Data.numRequests || 0,
        '总请求次数': gpt4Data.numRequestsTotal || 0,
        '使用令牌数': gpt4Data.numTokens || 0,
        '最大请求使用限制': gpt4Data.maxRequestUsage || '无限制',
        '最大令牌使用限制': gpt4Data.maxTokenUsage || '无限制',
        '本月开始日期': usageData.startOfMonth || '未知'
      };
      
      console.log('GPT-4 使用情况:');
      console.log(JSON.stringify(translatedData, null, 2));
      
      // 判断是否符合条件
      if (gpt4Data.numRequestsTotal < 30 && gpt4Data.maxRequestUsage >= 150) {
        console.log('\n✅ 该token符合条件: 总请求次数 < 30 且 最大请求限制 >= 150');
      } else {
        console.log('\n❌ 该token不符合条件');
        if (gpt4Data.numRequestsTotal >= 30) {
          console.log(`- 总请求次数(${gpt4Data.numRequestsTotal})已达到或超过30次`);
        }
        if (gpt4Data.maxRequestUsage < 150) {
          console.log(`- 最大请求限制(${gpt4Data.maxRequestUsage})小于150`);
        }
      }
    } else {
      console.log('无法获取GPT-4数据');
    }
  } catch (error) {
    console.error('执行失败:', error.message);
  }
}

// 执行脚本
testToken(); 