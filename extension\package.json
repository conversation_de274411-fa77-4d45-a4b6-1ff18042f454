{"name": "cursor-sun", "displayName": "Cursor-Sun", "description": "Cursor-Sun 增强工具，提供多种功能增强Cursor编辑器体验", "version": "2.19.2", "publisher": "cursor-sun", "icon": "icon.png", "repository": {"type": "git", "url": "https://github.com/cursor-sun/cursor-sun-vscode.git"}, "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onView:cursor-sun-sidebar", "onCommand:cursor-sun.showPanel", "onCommand:cursor-sun.showAiChat", "onCommand:cursor-sun.checkUserInfo", "onCommand:cursor-sun.checkTokenUsage", "onCommand:cursor-sun.checkAndFixPluginState", "onCommand:cursor-sun.testTokenFunctionModify", "onCommand:cursor-sun.checkDisableHttp2Setting"], "main": "./extension.js", "contributes": {"commands": [{"command": "cursor-sun.showPanel", "title": "Show Cursor-Sun Panel"}, {"command": "cursor-sun.showAiChat", "title": "Cursor-Sun: 打开AI聊天"}, {"command": "cursor-sun.checkUserInfo", "title": "Cursor-Sun: 检查用户信息"}, {"command": "cursor-sun.checkTokenUsage", "title": "Cursor-Sun: 检查令牌使用情况"}, {"command": "cursor-sun.checkAndFixPluginState", "title": "Cursor-Sun: 检查和修复插件状态"}, {"command": "cursor-sun.testTokenFunctionModify", "title": "Cursor-Sun: 测试令牌函数修改"}, {"command": "cursor-sun.checkDisableHttp2Setting", "title": "Cursor-Sun: 检查和更新HTTP/2设置"}], "viewsContainers": {"activitybar": [{"id": "cursor-sun-sidebar-view", "title": "Cursor-Sun", "icon": "icon-sidebar.svg"}]}, "views": {"cursor-sun-sidebar-view": [{"id": "cursor-sun-sidebar", "name": "Cursor-Sun", "type": "webview"}]}}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "node ./test/runTest.js", "vscode:prepublish": "npm run esbuild-base -- --minify", "esbuild-base": "esbuild ./extension.js --bundle --outfile=dist/extension.js --external:vscode --format=cjs --platform=node --metafile=dist/meta.json", "esbuild": "npm run esbuild-base -- --sourcemap", "esbuild-watch": "npm run esbuild-base -- --sourcemap --watch", "package": "vsce package"}, "devDependencies": {"@electron/rebuild": "^4.0.1", "@types/vscode": "^1.60.0", "@vscode/vsce": "^2.15.0", "electron-rebuild": "^3.2.9", "esbuild": "^0.17.15", "eslint": "^8.36.0"}, "dependencies": {"better-sqlite3": "^9.4.3", "node-fetch": "^2.7.0"}}