const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');
const { execSync } = require('child_process');
const vscode = require('vscode');

/**
 * 获取storage.json文件路径
 * @returns {string|null} storage.json的路径，失败返回null
 */
function getStorageJsonPath() {
    try {
        if (process.platform === 'win32') {
            const appData = process.env.APPDATA;
            if (!appData) {
                console.log('[TokenUpdater] 无法获取APPDATA路径');
                return null;
            }
            return path.join(appData, 'Cursor', 'User', 'globalStorage', 'storage.json');
        }
        else if (process.platform === 'darwin') {
            const homeDir = os.homedir();
            return path.join(homeDir, 'Library', 'Application Support', 'Cursor', 'User', 'globalStorage', 'storage.json');
        }
        else {
            console.log(`[TokenUpdater] 不支持的操作系统: ${process.platform}`);
            return null;
        }
    }
    catch (error) {
        console.log('[TokenUpdater] 获取storage.json路径失败: ' + error.message);
        return null;
    }
}

/**
 * 生成SHA-256哈希（64个字符的十六进制）
 * @returns {string} 64个字符的十六进制字符串
 */
function generateSHA256Hash() {
    // 生成随机32字节并计算SHA-256哈希
    return crypto.createHash('sha256').update(crypto.randomBytes(32)).digest('hex');
}

/**
 * 生成SHA-512哈希（128个字符的十六进制）
 * @returns {string} 128个字符的十六进制字符串
 */
function generateSHA512Hash() {
    // 生成随机64字节并计算SHA-512哈希
    return crypto.createHash('sha512').update(crypto.randomBytes(64)).digest('hex');
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
function generateUUID() {
    // 生成符合RFC4122的UUID v4
    return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) => 
        (c ^ crypto.randomBytes(1)[0] & 15 >> c / 4).toString(16)
    );
}

/**
 * 检查文件是否可写
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} 文件是否可写
 */
async function isFileWritable(filePath) {
    try {
        // 检查文件是否存在
        await fs.promises.access(filePath, fs.constants.F_OK);
        
        // 尝试打开文件进行写入测试
        const fd = await fs.promises.open(filePath, 'r+');
        await fd.close();
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 修改storage.json文件
 * @returns {Promise<boolean>} 是否成功
 */
async function modifyStorageJson() {
    try {
        // 获取storage.json文件路径
        const storageJsonPath = getStorageJsonPath();
        if (!storageJsonPath) {
            console.log('[TokenUpdater] 获取storage.json路径失败');
            return false;
        }
        
        console.log(`[TokenUpdater] storage.json路径: ${storageJsonPath}`);
        
        if (!fs.existsSync(storageJsonPath)) {
            console.log('[TokenUpdater] storage.json文件不存在');
            return false;
        }
        
        // 检查文件是否可写
        const writable = await isFileWritable(storageJsonPath);
        
        // 如果不可写，尝试移除文件的只读属性
        if (!writable) {
            try {
                if (process.platform === 'win32') {
                    execSync(`attrib -R "${storageJsonPath}"`);
                }
                else {
                    fs.chmodSync(storageJsonPath, 0o666);
                }
            }
            catch (error) {
                console.log(`[TokenUpdater] 警告: 修改文件权限失败: ${error.message}`);
                // 继续尝试修改文件
            }
        }
        
        // 备份文件
        const backupPath = storageJsonPath + '.backup';
        if (!fs.existsSync(backupPath)) {
            fs.copyFileSync(storageJsonPath, backupPath);
            console.log(`[TokenUpdater] 已创建备份: ${backupPath}`);
        }
        
        // 读取文件内容
        const content = fs.readFileSync(storageJsonPath, 'utf8');
        
        // 生成新的ID
        const newMachineId = generateSHA256Hash(); // 64个字符的十六进制
        const newMacMachineId = generateSHA512Hash(); // 128个字符的十六进制
        const newSqmId = "{" + generateUUID().toUpperCase() + "}";
        const newDevDeviceId = generateUUID();
        
        // 替换内容
        let contentStr = content;
        contentStr = contentStr.replace(/"telemetry\.machineId"\s*:\s*"[^"]*"/g, `"telemetry.machineId": "${newMachineId}"`);
        contentStr = contentStr.replace(/"telemetry\.macMachineId"\s*:\s*"[^"]*"/g, `"telemetry.macMachineId": "${newMacMachineId}"`);
        contentStr = contentStr.replace(/"telemetry\.sqmId"\s*:\s*"[^"]*"/g, `"telemetry.sqmId": "${newSqmId}"`);
        contentStr = contentStr.replace(/"telemetry\.devDeviceId"\s*:\s*"[^"]*"/g, `"telemetry.devDeviceId": "${newDevDeviceId}"`);
        
        // 写入文件
        fs.writeFileSync(storageJsonPath, contentStr, { mode: 0o644 });
        
        console.log('[TokenUpdater] storage.json文件更新成功');
        return true;
    }
    catch (error) {
        console.log('[TokenUpdater] 修改storage.json文件失败: ' + error.message);
        if (error.stack) {
            console.log('[TokenUpdater] 错误堆栈: ' + error.stack);
        }
        return false;
    }
}

/**
 * 恢复storage.json文件
 * @returns {Promise<boolean>} 是否成功
 */
async function restoreStorageJson() {
    try {
        // 获取storage.json文件路径
        const storageJsonPath = getStorageJsonPath();
        if (!storageJsonPath) {
            console.log('[TokenUpdater] 获取storage.json路径失败');
            return false;
        }
        
        // 备份文件路径
        const backupPath = storageJsonPath + '.backup';
        
        // 检查备份文件是否存在
        if (!fs.existsSync(backupPath)) {
            console.log('[TokenUpdater] 备份文件不存在，无法恢复');
            return false;
        }
        
        // 检查文件是否可写
        const writable = await isFileWritable(storageJsonPath);
        
        // 如果不可写，尝试移除文件的只读属性
        if (!writable) {
            try {
                if (process.platform === 'win32') {
                    execSync(`attrib -R "${storageJsonPath}"`);
                }
                else {
                    fs.chmodSync(storageJsonPath, 0o666);
                }
            }
            catch (error) {
                console.log(`[TokenUpdater] 警告: 修改文件权限失败: ${error.message}`);
                // 继续尝试修改文件
            }
        }
        
        // 从备份恢复文件
        fs.copyFileSync(backupPath, storageJsonPath);
        
        console.log('[TokenUpdater] storage.json文件已恢复');
        return true;
    }
    catch (error) {
        console.log('[TokenUpdater] 恢复storage.json文件失败: ' + error.message);
        return false;
    }
}

module.exports = {
    modifyStorageJson,
    restoreStorageJson
}; 