<?xml version="1.0" encoding="utf-8"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension=".json" ContentType="application/json"/><Default Extension=".vsixmanifest" ContentType="text/xml"/><Default Extension=".js" ContentType="application/javascript"/><Default Extension=".map" ContentType="application/json"/><Default Extension=".svg" ContentType="image/svg+xml"/><Default Extension=".png" ContentType="image/png"/><Default Extension=".txt" ContentType="text/plain"/><Default Extension=".log" ContentType="text/plain"/><Default Extension=".md" ContentType="text/markdown"/><Default Extension=".new" ContentType="application/octet-stream"/><Default Extension=".gyp" ContentType="application/octet-stream"/><Default Extension=".node" ContentType="application/octet-stream"/><Default Extension=".gypi" ContentType="application/octet-stream"/><Default Extension=".sh" ContentType="application/x-sh"/><Default Extension=".c" ContentType="text/x-c"/><Default Extension=".h" ContentType="text/x-c"/><Default Extension=".cpp" ContentType="text/x-c"/><Default Extension=".hpp" ContentType="application/octet-stream"/><Default Extension=".mjs" ContentType="application/javascript"/><Default Extension=".yml" ContentType="text/yaml"/><Default Extension=".ts" ContentType="video/mp2t"/><Default Extension=".markdown" ContentType="text/markdown"/><Default Extension=".apache2" ContentType="application/octet-stream"/><Default Extension=".bsd" ContentType="application/octet-stream"/><Default Extension=".mit" ContentType="application/octet-stream"/><Default Extension=".tar" ContentType="application/x-tar"/><Default Extension=".xml" ContentType="application/xml"/><Default Extension=".iml" ContentType="application/octet-stream"/><Default Extension=".bnf" ContentType="application/octet-stream"/></Types>
