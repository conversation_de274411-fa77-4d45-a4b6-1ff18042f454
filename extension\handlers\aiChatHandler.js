/**
 * 处理来自AI聊天Webview的消息
 * @param {Object} message - 消息对象
 * @param {vscode.Webview} webview - Webview实例
 */
async function handleAiChatMessage(message, webview) {
    switch (message.command) {
        case 'sendMessage':
            try {
                const { userMessage, model, messageId } = message;
                
                // 创建请求头
                const headers = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer sk-kQKMTKyEQA7X6eZ_y201q2CuaqBWH-cPm8U2dvfwefzzOzaMV7-9U6uIXqE`
                };
                
                // 创建请求体
                const requestBody = {
                    model: model || 'deepseek-v3.1',
                    messages: [{ role: 'user', content: userMessage }],
                    stream: true
                };
                
                // 发送初始响应，表示开始流式传输
                webview.postMessage({ 
                    command: 'streamStart', 
                    messageId: messageId 
                });
                
                // 使用fetch API发送请求
                const response = await fetch('https://api.sunai.asia/v1/chat/completions', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
                
                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    
                    // 处理数据流
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.substring(6);
                            if (data === '[DONE]') continue;
                            
                            try {
                                const parsed = JSON.parse(data);
                                const content = parsed.choices[0]?.delta?.content || '';
                                
                                if (content) {
                                    webview.postMessage({ 
                                        command: 'streamChunk', 
                                        messageId: messageId, 
                                        content: content 
                                    });
                                }
                            } catch (e) {
                                console.error('解析流数据失败:', e);
                            }
                        }
                    }
                }
                
                // 发送完成消息
                webview.postMessage({ 
                    command: 'streamEnd', 
                    messageId: messageId 
                });
                
            } catch (error) {
                console.error('AI聊天API调用失败:', error);
                webview.postMessage({ 
                    command: 'error', 
                    messageId: message.messageId,
                    error: error.message 
                });
            }
            return;
    }
}

module.exports = { handleAiChatMessage }; 